{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "authorship_tag": "ABX9TyM/chn2Ox0VnmQkOpTJHWGU"}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "code", "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "%matplotlib inline\n", "\n", "#import the sklearn libraries\n", "from sklearn import preprocessing\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.preprocessing import MinMaxScaler\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.svm import SVC\n", "from sklearn.neighbors import KNeighborsClassifier\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.metrics import precision_score\n", "from sklearn.metrics import f1_score\n", "from sklearn.metrics import recall_score"], "metadata": {"id": "Dr23LOCYZcNE", "executionInfo": {"status": "ok", "timestamp": 1708934093146, "user_tz": -330, "elapsed": 3723, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}}, "execution_count": 2, "outputs": []}, {"cell_type": "code", "source": ["data=pd.read_csv('/content/indian_liver_patient.csv')"], "metadata": {"id": "yS95owNCaW06", "executionInfo": {"status": "ok", "timestamp": 1708934473005, "user_tz": -330, "elapsed": 746, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}}, "execution_count": 11, "outputs": []}, {"cell_type": "code", "source": ["data.shape"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "b_jlg4Bxap9h", "executionInfo": {"status": "ok", "timestamp": 1708934475725, "user_tz": -330, "elapsed": 578, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}, "outputId": "964aa6c3-e466-46d8-da07-2a4033225305"}, "execution_count": 12, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["(583, 11)"]}, "metadata": {}, "execution_count": 12}]}, {"cell_type": "code", "source": ["data.columns"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "CXDI0WpgavSn", "executionInfo": {"status": "ok", "timestamp": 1708934479178, "user_tz": -330, "elapsed": 457, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}, "outputId": "5c351531-b0f9-428f-f7cd-b052dc0594a9"}, "execution_count": 13, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["Index(['Age', 'Gender', 'Total_Bilirubin', 'Direct_Bilirubin',\n", "       'Alkaline_Phosphotase', 'Alamine_Aminotransferase',\n", "       'Aspartate_Aminotransferase', 'Total_Protiens', 'Albumin',\n", "       'Albumin_and_Globulin_Ratio', 'Dataset'],\n", "      dtype='object')"]}, "metadata": {}, "execution_count": 13}]}, {"cell_type": "code", "source": ["data.head()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 226}, "id": "SS7PZ1p6ayzb", "executionInfo": {"status": "ok", "timestamp": 1708934481647, "user_tz": -330, "elapsed": 475, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}, "outputId": "4054561b-6569-438c-dbfb-b147aef31319"}, "execution_count": 14, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["   Age  Gender  Total_Bilirubin  Direct_Bilirubin  Alkaline_Phosphotase  \\\n", "0   65  Female              0.7               0.1                   187   \n", "1   62    Male             10.9               5.5                   699   \n", "2   62    Male              7.3               4.1                   490   \n", "3   58    Male              1.0               0.4                   182   \n", "4   72    Male              3.9               2.0                   195   \n", "\n", "   Alamine_Aminotransferase  Aspartate_Aminotransferase  Total_Protiens  \\\n", "0                        16                          18             6.8   \n", "1                        64                         100             7.5   \n", "2                        60                          68             7.0   \n", "3                        14                          20             6.8   \n", "4                        27                          59             7.3   \n", "\n", "   Albumin  Albumin_and_Globulin_Ratio  Dataset  \n", "0      3.3                        0.90        1  \n", "1      3.2                        0.74        1  \n", "2      3.3                        0.89        1  \n", "3      3.4                        1.00        1  \n", "4      2.4                        0.40        1  "], "text/html": ["\n", "  <div id=\"df-01eb6c5e-2e51-4f27-9ce0-ec7b24b966ef\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Age</th>\n", "      <th>Gender</th>\n", "      <th>Total_Bilirubin</th>\n", "      <th>Direct_Bilirubin</th>\n", "      <th>Alkaline_Phosphotase</th>\n", "      <th>Alamine_Aminotransferase</th>\n", "      <th>Aspartate_Aminotransferase</th>\n", "      <th>Total_Protiens</th>\n", "      <th>Albumin</th>\n", "      <th>Albumin_and_Globulin_Ratio</th>\n", "      <th>Dataset</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>65</td>\n", "      <td>Female</td>\n", "      <td>0.7</td>\n", "      <td>0.1</td>\n", "      <td>187</td>\n", "      <td>16</td>\n", "      <td>18</td>\n", "      <td>6.8</td>\n", "      <td>3.3</td>\n", "      <td>0.90</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>62</td>\n", "      <td>Male</td>\n", "      <td>10.9</td>\n", "      <td>5.5</td>\n", "      <td>699</td>\n", "      <td>64</td>\n", "      <td>100</td>\n", "      <td>7.5</td>\n", "      <td>3.2</td>\n", "      <td>0.74</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>62</td>\n", "      <td>Male</td>\n", "      <td>7.3</td>\n", "      <td>4.1</td>\n", "      <td>490</td>\n", "      <td>60</td>\n", "      <td>68</td>\n", "      <td>7.0</td>\n", "      <td>3.3</td>\n", "      <td>0.89</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>58</td>\n", "      <td>Male</td>\n", "      <td>1.0</td>\n", "      <td>0.4</td>\n", "      <td>182</td>\n", "      <td>14</td>\n", "      <td>20</td>\n", "      <td>6.8</td>\n", "      <td>3.4</td>\n", "      <td>1.00</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>72</td>\n", "      <td>Male</td>\n", "      <td>3.9</td>\n", "      <td>2.0</td>\n", "      <td>195</td>\n", "      <td>27</td>\n", "      <td>59</td>\n", "      <td>7.3</td>\n", "      <td>2.4</td>\n", "      <td>0.40</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-01eb6c5e-2e51-4f27-9ce0-ec7b24b966ef')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-01eb6c5e-2e51-4f27-9ce0-ec7b24b966ef button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-01eb6c5e-2e51-4f27-9ce0-ec7b24b966ef');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-76fe6b03-3f76-4e0a-8f9b-fbdee38fc975\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-76fe6b03-3f76-4e0a-8f9b-fbdee38fc975')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-76fe6b03-3f76-4e0a-8f9b-fbdee38fc975 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "data", "summary": "{\n  \"name\": \"data\",\n  \"rows\": 583,\n  \"fields\": [\n    {\n      \"column\": \"Age\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 16,\n        \"min\": 4,\n        \"max\": 90,\n        \"num_unique_values\": 72,\n        \"samples\": [\n          46,\n          23,\n          63\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Gender\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 2,\n        \"samples\": [\n          \"Male\",\n          \"Female\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Total_Bilirubin\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 6.209521726180149,\n        \"min\": 0.4,\n        \"max\": 75.0,\n        \"num_unique_values\": 113,\n        \"samples\": [\n          4.9,\n          3.9\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Direct_Bilirubin\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 2.8084976176589644,\n        \"min\": 0.1,\n        \"max\": 19.7,\n        \"num_unique_values\": 80,\n        \"samples\": [\n          6.2,\n          0.1\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Alkaline_Phosphotase\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 242,\n        \"min\": 63,\n        \"max\": 2110,\n        \"num_unique_values\": 263,\n        \"samples\": [\n          386,\n          209\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Alamine_Aminotransferase\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 182,\n        \"min\": 10,\n        \"max\": 2000,\n        \"num_unique_values\": 152,\n        \"samples\": [\n          2000,\n          321\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Aspartate_Aminotransferase\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 288,\n        \"min\": 10,\n        \"max\": 4929,\n        \"num_unique_values\": 177,\n        \"samples\": [\n          66,\n          16\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Total_Protiens\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 1.0854514840234657,\n        \"min\": 2.7,\n        \"max\": 9.6,\n        \"num_unique_values\": 58,\n        \"samples\": [\n          6.8,\n          6.7\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Albumin\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.7955188059640255,\n        \"min\": 0.9,\n        \"max\": 5.5,\n        \"num_unique_values\": 40,\n        \"samples\": [\n          2.0,\n          1.9\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Albumin_and_Globulin_Ratio\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.3195921076723706,\n        \"min\": 0.3,\n        \"max\": 2.8,\n        \"num_unique_values\": 69,\n        \"samples\": [\n          1.6,\n          0.9\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Dataset\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0,\n        \"min\": 1,\n        \"max\": 2,\n        \"num_unique_values\": 2,\n        \"samples\": [\n          2,\n          1\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 14}]}, {"cell_type": "markdown", "source": ["**Exploratory Data Analysis**"], "metadata": {"id": "Lf9fyhSfa6YD"}}, {"cell_type": "code", "source": ["data.dtypes[data.dtypes=='object']"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "GqjC3zaba9FN", "executionInfo": {"status": "ok", "timestamp": 1708934490631, "user_tz": -330, "elapsed": 949, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}, "outputId": "4fe81b6b-deb4-4e3d-9284-9a162453a02c"}, "execution_count": 15, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["Gender    object\n", "dtype: object"]}, "metadata": {}, "execution_count": 15}]}, {"cell_type": "code", "source": ["data.tail()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 226}, "id": "pUPn1WEBa23M", "executionInfo": {"status": "ok", "timestamp": 1708934493673, "user_tz": -330, "elapsed": 744, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}, "outputId": "0f3c3b99-de8f-4b57-a4ef-cfd88fdec44c"}, "execution_count": 16, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["     Age Gender  Total_Bilirubin  Direct_Bilirubin  Alkaline_Phosphotase  \\\n", "578   60   Male              0.5               0.1                   500   \n", "579   40   Male              0.6               0.1                    98   \n", "580   52   Male              0.8               0.2                   245   \n", "581   31   Male              1.3               0.5                   184   \n", "582   38   Male              1.0               0.3                   216   \n", "\n", "     Alamine_Aminotransferase  Aspartate_Aminotransferase  Total_Protiens  \\\n", "578                        20                          34             5.9   \n", "579                        35                          31             6.0   \n", "580                        48                          49             6.4   \n", "581                        29                          32             6.8   \n", "582                        21                          24             7.3   \n", "\n", "     Albumin  Albumin_and_Globulin_Ratio  Dataset  \n", "578      1.6                        0.37        2  \n", "579      3.2                        1.10        1  \n", "580      3.2                        1.00        1  \n", "581      3.4                        1.00        1  \n", "582      4.4                        1.50        2  "], "text/html": ["\n", "  <div id=\"df-50053e7d-2977-43ab-8223-5576986c92bf\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Age</th>\n", "      <th>Gender</th>\n", "      <th>Total_Bilirubin</th>\n", "      <th>Direct_Bilirubin</th>\n", "      <th>Alkaline_Phosphotase</th>\n", "      <th>Alamine_Aminotransferase</th>\n", "      <th>Aspartate_Aminotransferase</th>\n", "      <th>Total_Protiens</th>\n", "      <th>Albumin</th>\n", "      <th>Albumin_and_Globulin_Ratio</th>\n", "      <th>Dataset</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>578</th>\n", "      <td>60</td>\n", "      <td>Male</td>\n", "      <td>0.5</td>\n", "      <td>0.1</td>\n", "      <td>500</td>\n", "      <td>20</td>\n", "      <td>34</td>\n", "      <td>5.9</td>\n", "      <td>1.6</td>\n", "      <td>0.37</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>579</th>\n", "      <td>40</td>\n", "      <td>Male</td>\n", "      <td>0.6</td>\n", "      <td>0.1</td>\n", "      <td>98</td>\n", "      <td>35</td>\n", "      <td>31</td>\n", "      <td>6.0</td>\n", "      <td>3.2</td>\n", "      <td>1.10</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>580</th>\n", "      <td>52</td>\n", "      <td>Male</td>\n", "      <td>0.8</td>\n", "      <td>0.2</td>\n", "      <td>245</td>\n", "      <td>48</td>\n", "      <td>49</td>\n", "      <td>6.4</td>\n", "      <td>3.2</td>\n", "      <td>1.00</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>581</th>\n", "      <td>31</td>\n", "      <td>Male</td>\n", "      <td>1.3</td>\n", "      <td>0.5</td>\n", "      <td>184</td>\n", "      <td>29</td>\n", "      <td>32</td>\n", "      <td>6.8</td>\n", "      <td>3.4</td>\n", "      <td>1.00</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>582</th>\n", "      <td>38</td>\n", "      <td>Male</td>\n", "      <td>1.0</td>\n", "      <td>0.3</td>\n", "      <td>216</td>\n", "      <td>21</td>\n", "      <td>24</td>\n", "      <td>7.3</td>\n", "      <td>4.4</td>\n", "      <td>1.50</td>\n", "      <td>2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-50053e7d-2977-43ab-8223-5576986c92bf')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-50053e7d-2977-43ab-8223-5576986c92bf button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-50053e7d-2977-43ab-8223-5576986c92bf');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-314259f3-7f96-4976-a68c-e2de49b0eee5\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-314259f3-7f96-4976-a68c-e2de49b0eee5')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-314259f3-7f96-4976-a68c-e2de49b0eee5 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "summary": "{\n  \"name\": \"data\",\n  \"rows\": 5,\n  \"fields\": [\n    {\n      \"column\": \"Age\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 11,\n        \"min\": 31,\n        \"max\": 60,\n        \"num_unique_values\": 5,\n        \"samples\": [\n          40,\n          38,\n          52\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Gender\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 1,\n        \"samples\": [\n          \"Male\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Total_Bilirubin\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.3209361307176243,\n        \"min\": 0.5,\n        \"max\": 1.3,\n        \"num_unique_values\": 5,\n        \"samples\": [\n          0.6\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Direct_Bilirubin\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.1673320053068151,\n        \"min\": 0.1,\n        \"max\": 0.5,\n        \"num_unique_values\": 4,\n        \"samples\": [\n          0.2\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Alkaline_Phosphotase\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 150,\n        \"min\": 98,\n        \"max\": 500,\n        \"num_unique_values\": 5,\n        \"samples\": [\n          98\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Alamine_Aminotransferase\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 11,\n        \"min\": 20,\n        \"max\": 48,\n        \"num_unique_values\": 5,\n        \"samples\": [\n          35\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Aspartate_Aminotransferase\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 9,\n        \"min\": 24,\n        \"max\": 49,\n        \"num_unique_values\": 5,\n        \"samples\": [\n          31\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Total_Protiens\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.5805170109479995,\n        \"min\": 5.9,\n        \"max\": 7.3,\n        \"num_unique_values\": 5,\n        \"samples\": [\n          6.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Albumin\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 1.0039920318408906,\n        \"min\": 1.6,\n        \"max\": 4.4,\n        \"num_unique_values\": 4,\n        \"samples\": [\n          3.2\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Albumin_and_Globulin_Ratio\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.40519131283876264,\n        \"min\": 0.37,\n        \"max\": 1.5,\n        \"num_unique_values\": 4,\n        \"samples\": [\n          1.1\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Dataset\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0,\n        \"min\": 1,\n        \"max\": 2,\n        \"num_unique_values\": 2,\n        \"samples\": [\n          1\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 16}]}, {"cell_type": "code", "source": ["# help to know if there are missing data or not\n", "data.info()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "AoACbgqmbSBi", "executionInfo": {"status": "ok", "timestamp": 1708934509311, "user_tz": -330, "elapsed": 474, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}, "outputId": "e8475469-645b-413f-9f5a-84dbf6bce160"}, "execution_count": 17, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 583 entries, 0 to 582\n", "Data columns (total 11 columns):\n", " #   Column                      Non-Null Count  Dtype  \n", "---  ------                      --------------  -----  \n", " 0   Age                         583 non-null    int64  \n", " 1   Gender                      583 non-null    object \n", " 2   Total_Bilirubin             583 non-null    float64\n", " 3   Direct_Bilirubin            583 non-null    float64\n", " 4   Alkaline_Phosphotase        583 non-null    int64  \n", " 5   Alamine_Aminotransferase    583 non-null    int64  \n", " 6   Aspartate_Aminotransferase  583 non-null    int64  \n", " 7   Total_Protiens              583 non-null    float64\n", " 8   Albumin                     583 non-null    float64\n", " 9   Albumin_and_Globulin_Ratio  579 non-null    float64\n", " 10  Dataset                     583 non-null    int64  \n", "dtypes: float64(5), int64(5), object(1)\n", "memory usage: 50.2+ KB\n"]}]}, {"cell_type": "code", "source": ["data.describe()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 320}, "id": "1KQahzEMby5u", "executionInfo": {"status": "ok", "timestamp": 1708934529738, "user_tz": -330, "elapsed": 458, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}, "outputId": "a656b9ae-fff9-427a-b8f0-2b197c86c7b0"}, "execution_count": 18, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["              Age  Total_Bilirubin  Direct_Bilirubin  Alkaline_Phosphotase  \\\n", "count  583.000000       583.000000        583.000000            583.000000   \n", "mean    44.746141         3.298799          1.486106            290.576329   \n", "std     16.189833         6.209522          2.808498            242.937989   \n", "min      4.000000         0.400000          0.100000             63.000000   \n", "25%     33.000000         0.800000          0.200000            175.500000   \n", "50%     45.000000         1.000000          0.300000            208.000000   \n", "75%     58.000000         2.600000          1.300000            298.000000   \n", "max     90.000000        75.000000         19.700000           2110.000000   \n", "\n", "       Alamine_Aminotransferase  Aspartate_Aminotransferase  Total_Protiens  \\\n", "count                583.000000                  583.000000      583.000000   \n", "mean                  80.713551                  109.910806        6.483190   \n", "std                  182.620356                  288.918529        1.085451   \n", "min                   10.000000                   10.000000        2.700000   \n", "25%                   23.000000                   25.000000        5.800000   \n", "50%                   35.000000                   42.000000        6.600000   \n", "75%                   60.500000                   87.000000        7.200000   \n", "max                 2000.000000                 4929.000000        9.600000   \n", "\n", "          Albumin  Albumin_and_Globulin_Ratio     Dataset  \n", "count  583.000000                  579.000000  583.000000  \n", "mean     3.141852                    0.947064    1.286449  \n", "std      0.795519                    0.319592    0.452490  \n", "min      0.900000                    0.300000    1.000000  \n", "25%      2.600000                    0.700000    1.000000  \n", "50%      3.100000                    0.930000    1.000000  \n", "75%      3.800000                    1.100000    2.000000  \n", "max      5.500000                    2.800000    2.000000  "], "text/html": ["\n", "  <div id=\"df-d5d447e3-43d8-48ea-88f1-a1229dd6c63e\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Age</th>\n", "      <th>Total_Bilirubin</th>\n", "      <th>Direct_Bilirubin</th>\n", "      <th>Alkaline_Phosphotase</th>\n", "      <th>Alamine_Aminotransferase</th>\n", "      <th>Aspartate_Aminotransferase</th>\n", "      <th>Total_Protiens</th>\n", "      <th>Albumin</th>\n", "      <th>Albumin_and_Globulin_Ratio</th>\n", "      <th>Dataset</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>583.000000</td>\n", "      <td>583.000000</td>\n", "      <td>583.000000</td>\n", "      <td>583.000000</td>\n", "      <td>583.000000</td>\n", "      <td>583.000000</td>\n", "      <td>583.000000</td>\n", "      <td>583.000000</td>\n", "      <td>579.000000</td>\n", "      <td>583.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>44.746141</td>\n", "      <td>3.298799</td>\n", "      <td>1.486106</td>\n", "      <td>290.576329</td>\n", "      <td>80.713551</td>\n", "      <td>109.910806</td>\n", "      <td>6.483190</td>\n", "      <td>3.141852</td>\n", "      <td>0.947064</td>\n", "      <td>1.286449</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>16.189833</td>\n", "      <td>6.209522</td>\n", "      <td>2.808498</td>\n", "      <td>242.937989</td>\n", "      <td>182.620356</td>\n", "      <td>288.918529</td>\n", "      <td>1.085451</td>\n", "      <td>0.795519</td>\n", "      <td>0.319592</td>\n", "      <td>0.452490</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>4.000000</td>\n", "      <td>0.400000</td>\n", "      <td>0.100000</td>\n", "      <td>63.000000</td>\n", "      <td>10.000000</td>\n", "      <td>10.000000</td>\n", "      <td>2.700000</td>\n", "      <td>0.900000</td>\n", "      <td>0.300000</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>33.000000</td>\n", "      <td>0.800000</td>\n", "      <td>0.200000</td>\n", "      <td>175.500000</td>\n", "      <td>23.000000</td>\n", "      <td>25.000000</td>\n", "      <td>5.800000</td>\n", "      <td>2.600000</td>\n", "      <td>0.700000</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>45.000000</td>\n", "      <td>1.000000</td>\n", "      <td>0.300000</td>\n", "      <td>208.000000</td>\n", "      <td>35.000000</td>\n", "      <td>42.000000</td>\n", "      <td>6.600000</td>\n", "      <td>3.100000</td>\n", "      <td>0.930000</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>58.000000</td>\n", "      <td>2.600000</td>\n", "      <td>1.300000</td>\n", "      <td>298.000000</td>\n", "      <td>60.500000</td>\n", "      <td>87.000000</td>\n", "      <td>7.200000</td>\n", "      <td>3.800000</td>\n", "      <td>1.100000</td>\n", "      <td>2.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>90.000000</td>\n", "      <td>75.000000</td>\n", "      <td>19.700000</td>\n", "      <td>2110.000000</td>\n", "      <td>2000.000000</td>\n", "      <td>4929.000000</td>\n", "      <td>9.600000</td>\n", "      <td>5.500000</td>\n", "      <td>2.800000</td>\n", "      <td>2.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-d5d447e3-43d8-48ea-88f1-a1229dd6c63e')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-d5d447e3-43d8-48ea-88f1-a1229dd6c63e button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-d5d447e3-43d8-48ea-88f1-a1229dd6c63e');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-5b2db97e-6138-40f3-91f6-ff3dc46549fb\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-5b2db97e-6138-40f3-91f6-ff3dc46549fb')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-5b2db97e-6138-40f3-91f6-ff3dc46549fb button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "summary": "{\n  \"name\": \"data\",\n  \"rows\": 8,\n  \"fields\": [\n    {\n      \"column\": \"Age\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 193.19746263630807,\n        \"min\": 4.0,\n        \"max\": 583.0,\n        \"num_unique_values\": 8,\n        \"samples\": [\n          44.74614065180103,\n          45.0,\n          583.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Total_Bilirubin\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 203.21423966540334,\n        \"min\": 0.4,\n        \"max\": 583.0,\n        \"num_unique_values\": 8,\n        \"samples\": [\n          3.298799313893653,\n          1.0,\n          583.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Direct_Bilirubin\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 204.91982276311322,\n        \"min\": 0.1,\n        \"max\": 583.0,\n        \"num_unique_values\": 8,\n        \"samples\": [\n          1.486106346483705,\n          0.3,\n          583.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Alkaline_Phosphotase\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 668.8113528628011,\n        \"min\": 63.0,\n        \"max\": 2110.0,\n        \"num_unique_values\": 8,\n        \"samples\": [\n          290.57632933104634,\n          208.0,\n          583.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Alamine_Aminotransferase\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 684.4250480910189,\n        \"min\": 10.0,\n        \"max\": 2000.0,\n        \"num_unique_values\": 8,\n        \"samples\": [\n          80.71355060034305,\n          35.0,\n          583.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Aspartate_Aminotransferase\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 1695.6919519024464,\n        \"min\": 10.0,\n        \"max\": 4929.0,\n        \"num_unique_values\": 8,\n        \"samples\": [\n          109.91080617495712,\n          42.0,\n          583.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Total_Protiens\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 204.14536092043835,\n        \"min\": 1.0854514840234657,\n        \"max\": 583.0,\n        \"num_unique_values\": 8,\n        \"samples\": [\n          6.483190394511149,\n          6.6,\n          583.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Albumin\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 205.12531433205643,\n        \"min\": 0.7955188059640255,\n        \"max\": 583.0,\n        \"num_unique_values\": 8,\n        \"samples\": [\n          3.141852487135506,\n          3.1,\n          583.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Albumin_and_Globulin_Ratio\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 204.35048068584433,\n        \"min\": 0.3,\n        \"max\": 579.0,\n        \"num_unique_values\": 8,\n        \"samples\": [\n          0.9470639032815197,\n          0.93,\n          579.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Dataset\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 205.68092075096487,\n        \"min\": 0.4524901515081165,\n        \"max\": 583.0,\n        \"num_unique_values\": 5,\n        \"samples\": [\n          1.2864493996569468,\n          2.0,\n          0.4524901515081165\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 18}]}, {"cell_type": "code", "source": ["#commence data preprocessing\n", "\n", "#check to see if there are duplicates\n", "''' Duplicates are removed as it's most likely these entries has been inputed twice.'''\n", "data_duplicate = data[data.duplicated(keep = False)]\n", "# keep = False gives you all rows with duplicate entries\n", "data_duplicate"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 884}, "id": "92EaEdnDb35e", "executionInfo": {"status": "ok", "timestamp": 1708934546504, "user_tz": -330, "elapsed": 737, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}, "outputId": "9fe66478-fab3-484d-ca47-a0774ea82574"}, "execution_count": 19, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["     Age  Gender  Total_Bilirubin  Direct_Bilirubin  Alkaline_Phosphotase  \\\n", "18    40  Female              0.9               0.3                   293   \n", "19    40  Female              0.9               0.3                   293   \n", "25    34    Male              4.1               2.0                   289   \n", "26    34    Male              4.1               2.0                   289   \n", "33    38  Female              2.6               1.2                   410   \n", "34    38  Female              2.6               1.2                   410   \n", "54    42    Male              8.9               4.5                   272   \n", "55    42    Male              8.9               4.5                   272   \n", "61    58    Male              1.0               0.5                   158   \n", "62    58    Male              1.0               0.5                   158   \n", "105   36    Male              5.3               2.3                   145   \n", "106   36    Male              5.3               2.3                   145   \n", "107   36    Male              0.8               0.2                   158   \n", "108   36    Male              0.8               0.2                   158   \n", "137   18    Male              0.8               0.2                   282   \n", "138   18    Male              0.8               0.2                   282   \n", "142   30    Male              1.6               0.4                   332   \n", "143   30    Male              1.6               0.4                   332   \n", "157   72    Male              0.7               0.1                   196   \n", "158   72    Male              0.7               0.1                   196   \n", "163   39    Male              1.9               0.9                   180   \n", "164   39    Male              1.9               0.9                   180   \n", "173   31    Male              0.6               0.1                   175   \n", "174   31    Male              0.6               0.1                   175   \n", "200   49    Male              0.6               0.1                   218   \n", "201   49    Male              0.6               0.1                   218   \n", "\n", "     Alamine_Aminotransferase  Aspartate_Aminotransferase  Total_Protiens  \\\n", "18                        232                         245             6.8   \n", "19                        232                         245             6.8   \n", "25                        875                         731             5.0   \n", "26                        875                         731             5.0   \n", "33                         59                          57             5.6   \n", "34                         59                          57             5.6   \n", "54                         31                          61             5.8   \n", "55                         31                          61             5.8   \n", "61                         37                          43             7.2   \n", "62                         37                          43             7.2   \n", "105                        32                          92             5.1   \n", "106                        32                          92             5.1   \n", "107                        29                          39             6.0   \n", "108                        29                          39             6.0   \n", "137                        72                         140             5.5   \n", "138                        72                         140             5.5   \n", "142                        84                         139             5.6   \n", "143                        84                         139             5.6   \n", "157                        20                          35             5.8   \n", "158                        20                          35             5.8   \n", "163                        42                          62             7.4   \n", "164                        42                          62             7.4   \n", "173                        48                          34             6.0   \n", "174                        48                          34             6.0   \n", "200                        50                          53             5.0   \n", "201                        50                          53             5.0   \n", "\n", "     Albumin  Albumin_and_Globulin_Ratio  Dataset  \n", "18       3.1                        0.80        1  \n", "19       3.1                        0.80        1  \n", "25       2.7                        1.10        1  \n", "26       2.7                        1.10        1  \n", "33       3.0                        0.80        2  \n", "34       3.0                        0.80        2  \n", "54       2.0                        0.50        1  \n", "55       2.0                        0.50        1  \n", "61       3.6                        1.00        1  \n", "62       3.6                        1.00        1  \n", "105      2.6                        1.00        2  \n", "106      2.6                        1.00        2  \n", "107      2.2                        0.50        2  \n", "108      2.2                        0.50        2  \n", "137      2.5                        0.80        1  \n", "138      2.5                        0.80        1  \n", "142      2.7                        0.90        1  \n", "143      2.7                        0.90        1  \n", "157      2.0                        0.50        1  \n", "158      2.0                        0.50        1  \n", "163      4.3                        1.38        1  \n", "164      4.3                        1.38        1  \n", "173      3.7                        1.60        1  \n", "174      3.7                        1.60        1  \n", "200      2.4                        0.90        1  \n", "201      2.4                        0.90        1  "], "text/html": ["\n", "  <div id=\"df-eef6f314-7771-4b52-9bfb-cda8eee34b77\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Age</th>\n", "      <th>Gender</th>\n", "      <th>Total_Bilirubin</th>\n", "      <th>Direct_Bilirubin</th>\n", "      <th>Alkaline_Phosphotase</th>\n", "      <th>Alamine_Aminotransferase</th>\n", "      <th>Aspartate_Aminotransferase</th>\n", "      <th>Total_Protiens</th>\n", "      <th>Albumin</th>\n", "      <th>Albumin_and_Globulin_Ratio</th>\n", "      <th>Dataset</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>40</td>\n", "      <td>Female</td>\n", "      <td>0.9</td>\n", "      <td>0.3</td>\n", "      <td>293</td>\n", "      <td>232</td>\n", "      <td>245</td>\n", "      <td>6.8</td>\n", "      <td>3.1</td>\n", "      <td>0.80</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>40</td>\n", "      <td>Female</td>\n", "      <td>0.9</td>\n", "      <td>0.3</td>\n", "      <td>293</td>\n", "      <td>232</td>\n", "      <td>245</td>\n", "      <td>6.8</td>\n", "      <td>3.1</td>\n", "      <td>0.80</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>34</td>\n", "      <td>Male</td>\n", "      <td>4.1</td>\n", "      <td>2.0</td>\n", "      <td>289</td>\n", "      <td>875</td>\n", "      <td>731</td>\n", "      <td>5.0</td>\n", "      <td>2.7</td>\n", "      <td>1.10</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>34</td>\n", "      <td>Male</td>\n", "      <td>4.1</td>\n", "      <td>2.0</td>\n", "      <td>289</td>\n", "      <td>875</td>\n", "      <td>731</td>\n", "      <td>5.0</td>\n", "      <td>2.7</td>\n", "      <td>1.10</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>38</td>\n", "      <td>Female</td>\n", "      <td>2.6</td>\n", "      <td>1.2</td>\n", "      <td>410</td>\n", "      <td>59</td>\n", "      <td>57</td>\n", "      <td>5.6</td>\n", "      <td>3.0</td>\n", "      <td>0.80</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>38</td>\n", "      <td>Female</td>\n", "      <td>2.6</td>\n", "      <td>1.2</td>\n", "      <td>410</td>\n", "      <td>59</td>\n", "      <td>57</td>\n", "      <td>5.6</td>\n", "      <td>3.0</td>\n", "      <td>0.80</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>54</th>\n", "      <td>42</td>\n", "      <td>Male</td>\n", "      <td>8.9</td>\n", "      <td>4.5</td>\n", "      <td>272</td>\n", "      <td>31</td>\n", "      <td>61</td>\n", "      <td>5.8</td>\n", "      <td>2.0</td>\n", "      <td>0.50</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>55</th>\n", "      <td>42</td>\n", "      <td>Male</td>\n", "      <td>8.9</td>\n", "      <td>4.5</td>\n", "      <td>272</td>\n", "      <td>31</td>\n", "      <td>61</td>\n", "      <td>5.8</td>\n", "      <td>2.0</td>\n", "      <td>0.50</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>61</th>\n", "      <td>58</td>\n", "      <td>Male</td>\n", "      <td>1.0</td>\n", "      <td>0.5</td>\n", "      <td>158</td>\n", "      <td>37</td>\n", "      <td>43</td>\n", "      <td>7.2</td>\n", "      <td>3.6</td>\n", "      <td>1.00</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>62</th>\n", "      <td>58</td>\n", "      <td>Male</td>\n", "      <td>1.0</td>\n", "      <td>0.5</td>\n", "      <td>158</td>\n", "      <td>37</td>\n", "      <td>43</td>\n", "      <td>7.2</td>\n", "      <td>3.6</td>\n", "      <td>1.00</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>105</th>\n", "      <td>36</td>\n", "      <td>Male</td>\n", "      <td>5.3</td>\n", "      <td>2.3</td>\n", "      <td>145</td>\n", "      <td>32</td>\n", "      <td>92</td>\n", "      <td>5.1</td>\n", "      <td>2.6</td>\n", "      <td>1.00</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>106</th>\n", "      <td>36</td>\n", "      <td>Male</td>\n", "      <td>5.3</td>\n", "      <td>2.3</td>\n", "      <td>145</td>\n", "      <td>32</td>\n", "      <td>92</td>\n", "      <td>5.1</td>\n", "      <td>2.6</td>\n", "      <td>1.00</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>107</th>\n", "      <td>36</td>\n", "      <td>Male</td>\n", "      <td>0.8</td>\n", "      <td>0.2</td>\n", "      <td>158</td>\n", "      <td>29</td>\n", "      <td>39</td>\n", "      <td>6.0</td>\n", "      <td>2.2</td>\n", "      <td>0.50</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>108</th>\n", "      <td>36</td>\n", "      <td>Male</td>\n", "      <td>0.8</td>\n", "      <td>0.2</td>\n", "      <td>158</td>\n", "      <td>29</td>\n", "      <td>39</td>\n", "      <td>6.0</td>\n", "      <td>2.2</td>\n", "      <td>0.50</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>137</th>\n", "      <td>18</td>\n", "      <td>Male</td>\n", "      <td>0.8</td>\n", "      <td>0.2</td>\n", "      <td>282</td>\n", "      <td>72</td>\n", "      <td>140</td>\n", "      <td>5.5</td>\n", "      <td>2.5</td>\n", "      <td>0.80</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>138</th>\n", "      <td>18</td>\n", "      <td>Male</td>\n", "      <td>0.8</td>\n", "      <td>0.2</td>\n", "      <td>282</td>\n", "      <td>72</td>\n", "      <td>140</td>\n", "      <td>5.5</td>\n", "      <td>2.5</td>\n", "      <td>0.80</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>142</th>\n", "      <td>30</td>\n", "      <td>Male</td>\n", "      <td>1.6</td>\n", "      <td>0.4</td>\n", "      <td>332</td>\n", "      <td>84</td>\n", "      <td>139</td>\n", "      <td>5.6</td>\n", "      <td>2.7</td>\n", "      <td>0.90</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>143</th>\n", "      <td>30</td>\n", "      <td>Male</td>\n", "      <td>1.6</td>\n", "      <td>0.4</td>\n", "      <td>332</td>\n", "      <td>84</td>\n", "      <td>139</td>\n", "      <td>5.6</td>\n", "      <td>2.7</td>\n", "      <td>0.90</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>157</th>\n", "      <td>72</td>\n", "      <td>Male</td>\n", "      <td>0.7</td>\n", "      <td>0.1</td>\n", "      <td>196</td>\n", "      <td>20</td>\n", "      <td>35</td>\n", "      <td>5.8</td>\n", "      <td>2.0</td>\n", "      <td>0.50</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>158</th>\n", "      <td>72</td>\n", "      <td>Male</td>\n", "      <td>0.7</td>\n", "      <td>0.1</td>\n", "      <td>196</td>\n", "      <td>20</td>\n", "      <td>35</td>\n", "      <td>5.8</td>\n", "      <td>2.0</td>\n", "      <td>0.50</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>163</th>\n", "      <td>39</td>\n", "      <td>Male</td>\n", "      <td>1.9</td>\n", "      <td>0.9</td>\n", "      <td>180</td>\n", "      <td>42</td>\n", "      <td>62</td>\n", "      <td>7.4</td>\n", "      <td>4.3</td>\n", "      <td>1.38</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>164</th>\n", "      <td>39</td>\n", "      <td>Male</td>\n", "      <td>1.9</td>\n", "      <td>0.9</td>\n", "      <td>180</td>\n", "      <td>42</td>\n", "      <td>62</td>\n", "      <td>7.4</td>\n", "      <td>4.3</td>\n", "      <td>1.38</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>173</th>\n", "      <td>31</td>\n", "      <td>Male</td>\n", "      <td>0.6</td>\n", "      <td>0.1</td>\n", "      <td>175</td>\n", "      <td>48</td>\n", "      <td>34</td>\n", "      <td>6.0</td>\n", "      <td>3.7</td>\n", "      <td>1.60</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>174</th>\n", "      <td>31</td>\n", "      <td>Male</td>\n", "      <td>0.6</td>\n", "      <td>0.1</td>\n", "      <td>175</td>\n", "      <td>48</td>\n", "      <td>34</td>\n", "      <td>6.0</td>\n", "      <td>3.7</td>\n", "      <td>1.60</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>200</th>\n", "      <td>49</td>\n", "      <td>Male</td>\n", "      <td>0.6</td>\n", "      <td>0.1</td>\n", "      <td>218</td>\n", "      <td>50</td>\n", "      <td>53</td>\n", "      <td>5.0</td>\n", "      <td>2.4</td>\n", "      <td>0.90</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>201</th>\n", "      <td>49</td>\n", "      <td>Male</td>\n", "      <td>0.6</td>\n", "      <td>0.1</td>\n", "      <td>218</td>\n", "      <td>50</td>\n", "      <td>53</td>\n", "      <td>5.0</td>\n", "      <td>2.4</td>\n", "      <td>0.90</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-eef6f314-7771-4b52-9bfb-cda8eee34b77')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-eef6f314-7771-4b52-9bfb-cda8eee34b77 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-eef6f314-7771-4b52-9bfb-cda8eee34b77');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-cd4add7d-077c-4003-96f9-a246f2d26539\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-cd4add7d-077c-4003-96f9-a246f2d26539')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-cd4add7d-077c-4003-96f9-a246f2d26539 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "  <div id=\"id_4aeb1c55-2051-4b85-a823-c3cdf4f0b206\">\n", "    <style>\n", "      .colab-df-generate {\n", "        background-color: #E8F0FE;\n", "        border: none;\n", "        border-radius: 50%;\n", "        cursor: pointer;\n", "        display: none;\n", "        fill: #1967D2;\n", "        height: 32px;\n", "        padding: 0 0 0 0;\n", "        width: 32px;\n", "      }\n", "\n", "      .colab-df-generate:hover {\n", "        background-color: #E2EBFA;\n", "        box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "        fill: #174EA6;\n", "      }\n", "\n", "      [theme=dark] .colab-df-generate {\n", "        background-color: #3B4455;\n", "        fill: #D2E3FC;\n", "      }\n", "\n", "      [theme=dark] .colab-df-generate:hover {\n", "        background-color: #434B5C;\n", "        box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "        filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "        fill: #FFFFFF;\n", "      }\n", "    </style>\n", "    <button class=\"colab-df-generate\" onclick=\"generateWithVariable('data_duplicate')\"\n", "            title=\"Generate code using this dataframe.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M7,19H8.4L18.45,9,17,7.55,7,17.6ZM5,21V16.75L18.45,3.32a2,2,0,0,1,2.83,0l1.4,1.43a1.91,1.91,0,0,1,.58,1.4,1.91,1.91,0,0,1-.58,1.4L9.25,21ZM18.45,9,17,7.55Zm-12,3A5.31,5.31,0,0,0,4.9,8.1,5.31,5.31,0,0,0,1,6.5,5.31,5.31,0,0,0,4.9,4.9,5.31,5.31,0,0,0,6.5,1,5.31,5.31,0,0,0,8.1,4.9,5.31,5.31,0,0,0,12,6.5,5.46,5.46,0,0,0,6.5,12Z\"/>\n", "  </svg>\n", "    </button>\n", "    <script>\n", "      (() => {\n", "      const buttonEl =\n", "        document.querySelector('#id_4aeb1c55-2051-4b85-a823-c3cdf4f0b206 button.colab-df-generate');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      buttonEl.onclick = () => {\n", "        google.colab.notebook.generateWithVariable('data_duplicate');\n", "      }\n", "      })();\n", "    </script>\n", "  </div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "data_duplicate", "summary": "{\n  \"name\": \"data_duplicate\",\n  \"rows\": 26,\n  \"fields\": [\n    {\n      \"column\": \"Age\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 13,\n        \"min\": 18,\n        \"max\": 72,\n        \"num_unique_values\": 12,\n        \"samples\": [\n          31,\n          39,\n          40\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Gender\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 2,\n        \"samples\": [\n          \"Male\",\n          \"Female\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Total_Bilirubin\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 2.417092977429388,\n        \"min\": 0.6,\n        \"max\": 8.9,\n        \"num_unique_values\": 11,\n        \"samples\": [\n          5.3,\n          0.9\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Direct_Bilirubin\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 1.2584728229699067,\n        \"min\": 0.1,\n        \"max\": 4.5,\n        \"num_unique_values\": 10,\n        \"samples\": [\n          0.1,\n          2.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Alkaline_Phosphotase\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 79,\n        \"min\": 145,\n        \"max\": 410,\n        \"num_unique_values\": 12,\n        \"samples\": [\n          175,\n          180\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Alamine_Aminotransferase\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 227,\n        \"min\": 20,\n        \"max\": 875,\n        \"num_unique_values\": 13,\n        \"samples\": [\n          48,\n          20\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Aspartate_Aminotransferase\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 185,\n        \"min\": 34,\n        \"max\": 731,\n        \"num_unique_values\": 13,\n        \"samples\": [\n          34,\n          35\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Total_Protiens\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.7688552929768135,\n        \"min\": 5.0,\n        \"max\": 7.4,\n        \"num_unique_values\": 9,\n        \"samples\": [\n          5.5,\n          5.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Albumin\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.6792756322844097,\n        \"min\": 2.0,\n        \"max\": 4.3,\n        \"num_unique_values\": 11,\n        \"samples\": [\n          2.6,\n          3.1\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Albumin_and_Globulin_Ratio\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.3212360742267521,\n        \"min\": 0.5,\n        \"max\": 1.6,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          0.8,\n          1.1\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Dataset\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0,\n        \"min\": 1,\n        \"max\": 2,\n        \"num_unique_values\": 2,\n        \"samples\": [\n          2,\n          1\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 19}]}, {"cell_type": "code", "source": ["data = data[~data.duplicated(subset = None, keep = 'first')]\n", "# Here, keep = 'first' ensures that only the first row is taken into the final dataset.\n", "# The '~' sign tells pandas to keep all values except the 13 duplicate values\n", "data.shape"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "yjytgk_Fb76m", "executionInfo": {"status": "ok", "timestamp": 1708934560238, "user_tz": -330, "elapsed": 475, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}, "outputId": "44939943-8eda-475a-f383-737a1c82158e"}, "execution_count": 20, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["(570, 11)"]}, "metadata": {}, "execution_count": 20}]}, {"cell_type": "code", "source": ["#checking if there are any NULL values in our Dataset\n", "data.isnull().values.any()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "5XtAuQIHb_V3", "executionInfo": {"status": "ok", "timestamp": 1708934575032, "user_tz": -330, "elapsed": 488, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}, "outputId": "cb2ce225-06fa-4f6c-fcc6-556730137bf2"}, "execution_count": 21, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["True"]}, "metadata": {}, "execution_count": 21}]}, {"cell_type": "code", "source": ["#removing null values\n", "# display number of null values by column# display number of null values by column\n", "print(data.isnull().sum())"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "tnDrenKdcC8k", "executionInfo": {"status": "ok", "timestamp": 1708934587420, "user_tz": -330, "elapsed": 522, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}, "outputId": "b08828f1-801a-4ed3-fb76-d8b5158045d6"}, "execution_count": 22, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Age                           0\n", "Gender                        0\n", "Total_Bilirubin               0\n", "Direct_Bilirubin              0\n", "Alkaline_Phosphotase          0\n", "Alamine_Aminotransferase      0\n", "Aspartate_Aminotransferase    0\n", "Total_Protiens                0\n", "Albumin                       0\n", "Albumin_and_Globulin_Ratio    4\n", "Dataset                       0\n", "dtype: int64\n"]}]}, {"cell_type": "code", "source": ["# We can see that the column 'Albumin_and_Globulin_Ratio' has 4 missing values\n", "# One way to deal with them can be to just directly remove these 4 values\n", "print (\"length before removing NaN values:%d\"%len(data))\n", "data_2 = data[pd.notnull(data['Albumin_and_Globulin_Ratio'])]\n", "print (\"length after removing NaN values:%d\"%len(data_2))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "cDwtqDA-cF9P", "executionInfo": {"status": "ok", "timestamp": 1708934600216, "user_tz": -330, "elapsed": 448, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}, "outputId": "ba153df3-8253-420e-9e3f-d357dad54639"}, "execution_count": 23, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["length before removing NaN values:570\n", "length after removing NaN values:566\n"]}]}, {"cell_type": "code", "source": ["new_data=data.dropna(axis = 0, how ='any')"], "metadata": {"id": "Vezay7L3cJGt", "executionInfo": {"status": "ok", "timestamp": 1708934612167, "user_tz": -330, "elapsed": 442, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}}, "execution_count": 24, "outputs": []}, {"cell_type": "code", "source": ["new_data.isnull().values.any()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "678LwwyicMBn", "executionInfo": {"status": "ok", "timestamp": 1708934623199, "user_tz": -330, "elapsed": 440, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}, "outputId": "bf9800a3-84ee-46c1-c3e4-8eae5e393f77"}, "execution_count": 25, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["False"]}, "metadata": {}, "execution_count": 25}]}, {"cell_type": "code", "source": ["data.info()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "1iijDKOKcOtj", "executionInfo": {"status": "ok", "timestamp": 1708934636281, "user_tz": -330, "elapsed": 420, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}, "outputId": "95adc91b-33ae-4e26-8537-fab28b6e8463"}, "execution_count": 26, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "Int64Index: 570 entries, 0 to 582\n", "Data columns (total 11 columns):\n", " #   Column                      Non-Null Count  Dtype  \n", "---  ------                      --------------  -----  \n", " 0   Age                         570 non-null    int64  \n", " 1   Gender                      570 non-null    object \n", " 2   Total_Bilirubin             570 non-null    float64\n", " 3   Direct_Bilirubin            570 non-null    float64\n", " 4   Alkaline_Phosphotase        570 non-null    int64  \n", " 5   Alamine_Aminotransferase    570 non-null    int64  \n", " 6   Aspartate_Aminotransferase  570 non-null    int64  \n", " 7   Total_Protiens              570 non-null    float64\n", " 8   Albumin                     570 non-null    float64\n", " 9   Albumin_and_Globulin_Ratio  566 non-null    float64\n", " 10  Dataset                     570 non-null    int64  \n", "dtypes: float64(5), int64(5), object(1)\n", "memory usage: 53.4+ KB\n"]}]}, {"cell_type": "code", "source": ["'''\n", "The Albumin-Globulin Ratio feature has four missing values, as seen above. Here, we are dropping those particular rows which have missing data. We could, in fact, fill those place with values of our own, using options like:\n", "\n", "A constant value that has meaning within the domain, such as 0, distinct from all other values.\n", "A value from another randomly selected record, or the immediately next or previous record.\n", "A mean, median or mode value for the column.\n", "A value estimated by another predictive model.\n", "But here, since a very small fraction of values are missing, we choose to drop those rows.\n", "'''\n", "#Transform our data\n", "le = preprocessing.LabelEncoder()\n", "le.fit(['Male','Female'])\n", "data.loc[:,'Gender'] = le.transform(data['Gender'])\n", "\n", "#Remove rows with missing values\n", "data = data.dropna(how = 'any', axis = 0)\n", "\n", "#Also transform Selector variable into usual conventions followed\n", "data['Dataset'] = data['Dataset'].map({2:0, 1:1})"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "HE2gN7AzcR5J", "executionInfo": {"status": "ok", "timestamp": 1708934653483, "user_tz": -330, "elapsed": 460, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}, "outputId": "7faf88a6-16e3-41b7-9e84-2f52145c7a5c"}, "execution_count": 27, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["<ipython-input-27-35f81ff54439>:13: Deprecation<PERSON>arning: In a future version, `df.iloc[:, i] = newvals` will attempt to set the values inplace instead of always setting a new array. To retain the old behavior, use either `df[df.columns[i]] = newvals` or, if columns are non-unique, `df.isetitem(i, newvals)`\n", "  data.loc[:,'Gender'] = le.transform(data['Gender'])\n", "<ipython-input-27-35f81ff54439>:19: Set<PERSON>WithC<PERSON>Warning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  data['Dataset'] = data['Dataset'].map({2:0, 1:1})\n"]}]}, {"cell_type": "code", "source": ["#Overview of data\n", "data.head()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 226}, "id": "RPQSxd56cWGm", "executionInfo": {"status": "ok", "timestamp": 1708934669696, "user_tz": -330, "elapsed": 435, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}, "outputId": "45b316b3-94c0-4a20-93df-965071fad9d2"}, "execution_count": 28, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["   Age  Gender  Total_Bilirubin  Direct_Bilirubin  Alkaline_Phosphotase  \\\n", "0   65       0              0.7               0.1                   187   \n", "1   62       1             10.9               5.5                   699   \n", "2   62       1              7.3               4.1                   490   \n", "3   58       1              1.0               0.4                   182   \n", "4   72       1              3.9               2.0                   195   \n", "\n", "   Alamine_Aminotransferase  Aspartate_Aminotransferase  Total_Protiens  \\\n", "0                        16                          18             6.8   \n", "1                        64                         100             7.5   \n", "2                        60                          68             7.0   \n", "3                        14                          20             6.8   \n", "4                        27                          59             7.3   \n", "\n", "   Albumin  Albumin_and_Globulin_Ratio  Dataset  \n", "0      3.3                        0.90        1  \n", "1      3.2                        0.74        1  \n", "2      3.3                        0.89        1  \n", "3      3.4                        1.00        1  \n", "4      2.4                        0.40        1  "], "text/html": ["\n", "  <div id=\"df-fd5320ed-1a5a-44b0-b901-195d820f4100\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Age</th>\n", "      <th>Gender</th>\n", "      <th>Total_Bilirubin</th>\n", "      <th>Direct_Bilirubin</th>\n", "      <th>Alkaline_Phosphotase</th>\n", "      <th>Alamine_Aminotransferase</th>\n", "      <th>Aspartate_Aminotransferase</th>\n", "      <th>Total_Protiens</th>\n", "      <th>Albumin</th>\n", "      <th>Albumin_and_Globulin_Ratio</th>\n", "      <th>Dataset</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>65</td>\n", "      <td>0</td>\n", "      <td>0.7</td>\n", "      <td>0.1</td>\n", "      <td>187</td>\n", "      <td>16</td>\n", "      <td>18</td>\n", "      <td>6.8</td>\n", "      <td>3.3</td>\n", "      <td>0.90</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>62</td>\n", "      <td>1</td>\n", "      <td>10.9</td>\n", "      <td>5.5</td>\n", "      <td>699</td>\n", "      <td>64</td>\n", "      <td>100</td>\n", "      <td>7.5</td>\n", "      <td>3.2</td>\n", "      <td>0.74</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>62</td>\n", "      <td>1</td>\n", "      <td>7.3</td>\n", "      <td>4.1</td>\n", "      <td>490</td>\n", "      <td>60</td>\n", "      <td>68</td>\n", "      <td>7.0</td>\n", "      <td>3.3</td>\n", "      <td>0.89</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>58</td>\n", "      <td>1</td>\n", "      <td>1.0</td>\n", "      <td>0.4</td>\n", "      <td>182</td>\n", "      <td>14</td>\n", "      <td>20</td>\n", "      <td>6.8</td>\n", "      <td>3.4</td>\n", "      <td>1.00</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>72</td>\n", "      <td>1</td>\n", "      <td>3.9</td>\n", "      <td>2.0</td>\n", "      <td>195</td>\n", "      <td>27</td>\n", "      <td>59</td>\n", "      <td>7.3</td>\n", "      <td>2.4</td>\n", "      <td>0.40</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-fd5320ed-1a5a-44b0-b901-195d820f4100')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-fd5320ed-1a5a-44b0-b901-195d820f4100 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-fd5320ed-1a5a-44b0-b901-195d820f4100');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-b93728bc-d984-4711-9e97-db8f5eb1c720\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-b93728bc-d984-4711-9e97-db8f5eb1c720')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-b93728bc-d984-4711-9e97-db8f5eb1c720 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "data", "summary": "{\n  \"name\": \"data\",\n  \"rows\": 566,\n  \"fields\": [\n    {\n      \"column\": \"Age\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 16,\n        \"min\": 4,\n        \"max\": 90,\n        \"num_unique_values\": 72,\n        \"samples\": [\n          46,\n          23,\n          63\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Gender\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0,\n        \"min\": 0,\n        \"max\": 1,\n        \"num_unique_values\": 2,\n        \"samples\": [\n          1,\n          0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Total_Bilirubin\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 6.286727820079474,\n        \"min\": 0.4,\n        \"max\": 75.0,\n        \"num_unique_values\": 113,\n        \"samples\": [\n          4.9,\n          3.9\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Direct_Bilirubin\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 2.841484953833559,\n        \"min\": 0.1,\n        \"max\": 19.7,\n        \"num_unique_values\": 80,\n        \"samples\": [\n          6.2,\n          0.1\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Alkaline_Phosphotase\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 245,\n        \"min\": 63,\n        \"max\": 2110,\n        \"num_unique_values\": 262,\n        \"samples\": [\n          768,\n          1550\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Alamine_Aminotransferase\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 182,\n        \"min\": 10,\n        \"max\": 2000,\n        \"num_unique_values\": 152,\n        \"samples\": [\n          2000,\n          321\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Aspartate_Aminotransferase\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 291,\n        \"min\": 10,\n        \"max\": 4929,\n        \"num_unique_values\": 177,\n        \"samples\": [\n          66,\n          16\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Total_Protiens\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 1.0875116854973899,\n        \"min\": 2.7,\n        \"max\": 9.6,\n        \"num_unique_values\": 58,\n        \"samples\": [\n          6.8,\n          6.7\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Albumin\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.7957452760901415,\n        \"min\": 0.9,\n        \"max\": 5.5,\n        \"num_unique_values\": 40,\n        \"samples\": [\n          2.0,\n          1.9\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Albumin_and_Globulin_Ratio\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.3196354228293773,\n        \"min\": 0.3,\n        \"max\": 2.8,\n        \"num_unique_values\": 69,\n        \"samples\": [\n          1.6,\n          0.9\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Dataset\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0,\n        \"min\": 0,\n        \"max\": 1,\n        \"num_unique_values\": 2,\n        \"samples\": [\n          0,\n          1\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 28}]}, {"cell_type": "code", "source": ["#features characteristics to determine if feature scaling is necessary\n", "data.describe()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 320}, "id": "3hQjNNhfcaEI", "executionInfo": {"status": "ok", "timestamp": 1708934684043, "user_tz": -330, "elapsed": 582, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}, "outputId": "45085343-091e-44c5-bb30-2b4c6beff752"}, "execution_count": 29, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["              Age      Gender  Total_Bilirubin  Direct_Bilirubin  \\\n", "count  566.000000  566.000000       566.000000        566.000000   \n", "mean    44.886926    0.756184         3.338869          1.505830   \n", "std     16.274893    0.429763         6.286728          2.841485   \n", "min      4.000000    0.000000         0.400000          0.100000   \n", "25%     33.000000    1.000000         0.800000          0.200000   \n", "50%     45.000000    1.000000         1.000000          0.300000   \n", "75%     58.000000    1.000000         2.600000          1.300000   \n", "max     90.000000    1.000000        75.000000         19.700000   \n", "\n", "       Alkaline_Phosphotase  Alamine_Aminotransferase  \\\n", "count            566.000000                566.000000   \n", "mean             292.567138                 80.143110   \n", "std              245.936559                182.044881   \n", "min               63.000000                 10.000000   \n", "25%              176.000000                 23.000000   \n", "50%              208.000000                 35.000000   \n", "75%              298.000000                 60.750000   \n", "max             2110.000000               2000.000000   \n", "\n", "       Aspartate_Aminotransferase  Total_Protiens     Albumin  \\\n", "count                  566.000000      566.000000  566.000000   \n", "mean                   109.892226        6.494876    3.145583   \n", "std                    291.841897        1.087512    0.795745   \n", "min                     10.000000        2.700000    0.900000   \n", "25%                     25.000000        5.800000    2.600000   \n", "50%                     41.000000        6.600000    3.100000   \n", "75%                     87.000000        7.200000    3.800000   \n", "max                   4929.000000        9.600000    5.500000   \n", "\n", "       Albumin_and_Globulin_Ratio     Dataset  \n", "count                  566.000000  566.000000  \n", "mean                     0.948004    0.713781  \n", "std                      0.319635    0.452393  \n", "min                      0.300000    0.000000  \n", "25%                      0.700000    0.000000  \n", "50%                      0.950000    1.000000  \n", "75%                      1.100000    1.000000  \n", "max                      2.800000    1.000000  "], "text/html": ["\n", "  <div id=\"df-c1eba874-176f-4709-a012-4004bf137fa4\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Age</th>\n", "      <th>Gender</th>\n", "      <th>Total_Bilirubin</th>\n", "      <th>Direct_Bilirubin</th>\n", "      <th>Alkaline_Phosphotase</th>\n", "      <th>Alamine_Aminotransferase</th>\n", "      <th>Aspartate_Aminotransferase</th>\n", "      <th>Total_Protiens</th>\n", "      <th>Albumin</th>\n", "      <th>Albumin_and_Globulin_Ratio</th>\n", "      <th>Dataset</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>566.000000</td>\n", "      <td>566.000000</td>\n", "      <td>566.000000</td>\n", "      <td>566.000000</td>\n", "      <td>566.000000</td>\n", "      <td>566.000000</td>\n", "      <td>566.000000</td>\n", "      <td>566.000000</td>\n", "      <td>566.000000</td>\n", "      <td>566.000000</td>\n", "      <td>566.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>44.886926</td>\n", "      <td>0.756184</td>\n", "      <td>3.338869</td>\n", "      <td>1.505830</td>\n", "      <td>292.567138</td>\n", "      <td>80.143110</td>\n", "      <td>109.892226</td>\n", "      <td>6.494876</td>\n", "      <td>3.145583</td>\n", "      <td>0.948004</td>\n", "      <td>0.713781</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>16.274893</td>\n", "      <td>0.429763</td>\n", "      <td>6.286728</td>\n", "      <td>2.841485</td>\n", "      <td>245.936559</td>\n", "      <td>182.044881</td>\n", "      <td>291.841897</td>\n", "      <td>1.087512</td>\n", "      <td>0.795745</td>\n", "      <td>0.319635</td>\n", "      <td>0.452393</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>4.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.400000</td>\n", "      <td>0.100000</td>\n", "      <td>63.000000</td>\n", "      <td>10.000000</td>\n", "      <td>10.000000</td>\n", "      <td>2.700000</td>\n", "      <td>0.900000</td>\n", "      <td>0.300000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>33.000000</td>\n", "      <td>1.000000</td>\n", "      <td>0.800000</td>\n", "      <td>0.200000</td>\n", "      <td>176.000000</td>\n", "      <td>23.000000</td>\n", "      <td>25.000000</td>\n", "      <td>5.800000</td>\n", "      <td>2.600000</td>\n", "      <td>0.700000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>45.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>0.300000</td>\n", "      <td>208.000000</td>\n", "      <td>35.000000</td>\n", "      <td>41.000000</td>\n", "      <td>6.600000</td>\n", "      <td>3.100000</td>\n", "      <td>0.950000</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>58.000000</td>\n", "      <td>1.000000</td>\n", "      <td>2.600000</td>\n", "      <td>1.300000</td>\n", "      <td>298.000000</td>\n", "      <td>60.750000</td>\n", "      <td>87.000000</td>\n", "      <td>7.200000</td>\n", "      <td>3.800000</td>\n", "      <td>1.100000</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>90.000000</td>\n", "      <td>1.000000</td>\n", "      <td>75.000000</td>\n", "      <td>19.700000</td>\n", "      <td>2110.000000</td>\n", "      <td>2000.000000</td>\n", "      <td>4929.000000</td>\n", "      <td>9.600000</td>\n", "      <td>5.500000</td>\n", "      <td>2.800000</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-c1eba874-176f-4709-a012-4004bf137fa4')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-c1eba874-176f-4709-a012-4004bf137fa4 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-c1eba874-176f-4709-a012-4004bf137fa4');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-63d530de-854f-4fd6-8753-a591b2dfefc5\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-63d530de-854f-4fd6-8753-a591b2dfefc5')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-63d530de-854f-4fd6-8753-a591b2dfefc5 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "summary": "{\n  \"name\": \"data\",\n  \"rows\": 8,\n  \"fields\": [\n    {\n      \"column\": \"Age\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 187.23129709019383,\n        \"min\": 4.0,\n        \"max\": 566.0,\n        \"num_unique_values\": 8,\n        \"samples\": [\n          44.886925795053,\n          45.0,\n          566.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Gender\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 199.84961606702922,\n        \"min\": 0.0,\n        \"max\": 566.0,\n        \"num_unique_values\": 5,\n        \"samples\": [\n          0.7561837455830389,\n          1.0,\n          0.4297629659309522\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Total_Bilirubin\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 197.2461811440823,\n        \"min\": 0.4,\n        \"max\": 566.0,\n        \"num_unique_values\": 8,\n        \"samples\": [\n          3.3388692579505297,\n          1.0,\n          566.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Direct_Bilirubin\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 198.90990729626077,\n        \"min\": 0.1,\n        \"max\": 566.0,\n        \"num_unique_values\": 8,\n        \"samples\": [\n          1.5058303886925795,\n          0.3,\n          566.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Alkaline_Phosphotase\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 668.2429659231988,\n        \"min\": 63.0,\n        \"max\": 2110.0,\n        \"num_unique_values\": 8,\n        \"samples\": [\n          292.5671378091873,\n          208.0,\n          566.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Alamine_Aminotransferase\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 683.7426974421059,\n        \"min\": 10.0,\n        \"max\": 2000.0,\n        \"num_unique_values\": 8,\n        \"samples\": [\n          80.14310954063605,\n          35.0,\n          566.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Aspartate_Aminotransferase\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 1695.9014791505379,\n        \"min\": 10.0,\n        \"max\": 4929.0,\n        \"num_unique_values\": 8,\n        \"samples\": [\n          109.89222614840989,\n          41.0,\n          566.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Total_Protiens\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 198.13478126419366,\n        \"min\": 1.0875116854973899,\n        \"max\": 566.0,\n        \"num_unique_values\": 8,\n        \"samples\": [\n          6.49487632508834,\n          6.6,\n          566.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Albumin\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 199.11487716461906,\n        \"min\": 0.7957452760901415,\n        \"max\": 566.0,\n        \"num_unique_values\": 8,\n        \"samples\": [\n          3.1455830388692583,\n          3.1,\n          566.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Albumin_and_Globulin_Ratio\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 199.75326025128874,\n        \"min\": 0.3,\n        \"max\": 566.0,\n        \"num_unique_values\": 8,\n        \"samples\": [\n          0.9480035335689044,\n          0.95,\n          566.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Dataset\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 199.90123665953502,\n        \"min\": 0.0,\n        \"max\": 566.0,\n        \"num_unique_values\": 5,\n        \"samples\": [\n          0.7137809187279152,\n          1.0,\n          0.4523928687221527\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 29}]}, {"cell_type": "code", "source": ["#split the data into test and train samples\n", "X_train, X_test, y_train, y_test = train_test_split(data, data['Dataset'], random_state = 0)\n", "scaler = MinMaxScaler()\n", "X_train_scaled = scaler.fit_transform(X_train)\n", "X_test_scaled = scaler.transform(X_test)"], "metadata": {"id": "tJcfZuQ6cdhb", "executionInfo": {"status": "ok", "timestamp": 1708934698056, "user_tz": -330, "elapsed": 444, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}}, "execution_count": 30, "outputs": []}, {"cell_type": "code", "source": ["#Exploratory Data Analysis\n", "\n", "#Determining the healthy-affected split\n", "print(\"Positive records:\", data['Dataset'].value_counts().iloc[0])\n", "print(\"Negative records:\", data['Dataset'].value_counts().iloc[1])"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "mM8D29X2cg_l", "executionInfo": {"status": "ok", "timestamp": 1708934712440, "user_tz": -330, "elapsed": 742, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}, "outputId": "7f00997e-0ee3-4d54-8629-0dd9b297c3ef"}, "execution_count": 31, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Positive records: 404\n", "Negative records: 162\n"]}]}, {"cell_type": "code", "source": ["#The above output confirms that we have 414 positive and 165 negative records. This indicates that this is a highly unbalanced dataset.\n", "\n", "#Determine statistics based on age\n", "plt.figure(figsize=(12, 10))\n", "plt.hist(data[data['Dataset'] == 1]['Age'], bins = 16, align = 'mid', rwidth = 0.5, color = 'black', alpha = 0.8)\n", "plt.xlabel('Age')\n", "plt.ylabel('Number of Patients')\n", "plt.title('Frequency-Age Distribution')\n", "plt.grid(True)\n", "plt.savefig('fig1')\n", "plt.show()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 872}, "id": "-wvGmP03ckYR", "executionInfo": {"status": "ok", "timestamp": 1708934724963, "user_tz": -330, "elapsed": 1182, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}, "outputId": "9aabe812-d174-403a-d69a-2bd9cff8724e"}, "execution_count": 32, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 1200x1000 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}]}, {"cell_type": "code", "source": ["#Looking at the age vs. frequency graph, we can observe that middle-aged people are the worst affected. Even elderly people are also suffering from liver ailments,\n", "#as seen by the bar sizes at ages 60-80.\n", "\n", "#correlation-matrix\n", "plt.subplots(figsize=(12, 10))\n", "plt.title('Pearson Correlation of Features')\n", "# Draw the heatmap using seaborn\n", "sns.heatmap(data.corr(),linewidths=0.25, vmax=1.0, square=True,annot=True)\n", "plt.savefig('fig2')\n", "plt.show()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "YBe23N6fcnYU", "executionInfo": {"status": "ok", "timestamp": 1708934746415, "user_tz": -330, "elapsed": 8273, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}, "outputId": "ddcadad4-0fc8-4bef-9ec1-0c6f5e36c65e"}, "execution_count": 33, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 1200x1000 with 2 Axes>"], "image/png": "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\n"}, "metadata": {}}]}, {"cell_type": "code", "source": ["'''\n", "The correlation matrix gives us the relationship between two features. As seen above, the following pairs of features seem to be very closely related as indicated by their high correlation coefficients:\n", "\n", "1.Total Bilirubin and Direct Bilirubin(0.87)\n", "2.Sgpt Alamine Aminotransferase and Sgot Aspartate Aminotransferase(0.79)\n", "3.<PERSON><PERSON> and <PERSON> Proteins(0.78)\n", "4.<PERSON><PERSON> and Albumin-<PERSON><PERSON><PERSON><PERSON>(0.69)\n", "'''\n", "\n", "'''\n", "#Using Classification Algorithms\n", "Let us now evaluate the performance of various classifiers on this dataset.\n", "For the sake of understanding as to how feature scaling affects classifier performance,\n", "we will train models using both scaled and unscaled data.\n", "Since we are interested in capturing records of people who have been tested positive,\n", "we will base our classifier evaluation metric on precision and recall instead of accuracy.\n", "We could also use F1 score, since it takes into account both precision and recall.\n", "'''\n", "\n", "#Logistic Regression: Using normal data\n", "logreg = LogisticRegression(C = 0.1).fit(X_train, y_train)\n", "print(\"Logistic Regression Classifier on unscaled test data:\")\n", "print(\"Accuracy:\", logreg.score(X_test, y_test))\n", "print(\"Precision:\", precision_score(y_test, logreg.predict(X_test)))\n", "print(\"Recall:\", recall_score(y_test, logreg.predict(X_test)))\n", "print(\"F-1 score:\", f1_score(y_test, logreg.predict(X_test)))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "trBa1rP5cq4U", "executionInfo": {"status": "ok", "timestamp": *************, "user_tz": -330, "elapsed": 1474, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}, "outputId": "f4a0fbd6-4e43-41e0-eaf5-ed1f7984ce55"}, "execution_count": 34, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Logistic Regression Classifier on unscaled test data:\n", "Accuracy: 0.***************5\n", "Precision: 0.9805825242718447\n", "Recall: 1.0\n", "F-1 score: 0.9901960784313726\n"]}, {"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.10/dist-packages/sklearn/linear_model/_logistic.py:458: ConvergenceWarning: lbfgs failed to converge (status=1):\n", "STOP: TOTAL NO. of ITERATIONS REACHED LIMIT.\n", "\n", "Increase the number of iterations (max_iter) or scale the data as shown in:\n", "    https://scikit-learn.org/stable/modules/preprocessing.html\n", "Please also refer to the documentation for alternative solver options:\n", "    https://scikit-learn.org/stable/modules/linear_model.html#logistic-regression\n", "  n_iter_i = _check_optimize_result(\n"]}]}, {"cell_type": "code", "source": ["#Using feature-scaled data\n", "logreg_scaled = LogisticRegression(C = 0.1).fit(X_train_scaled, y_train)\n", "print(\"Logistic Regression Classifier on scaled test data:\")\n", "print(\"Accuracy:\", logreg_scaled.score(X_test_scaled, y_test))\n", "print(\"Precision:\", precision_score(y_test, logreg_scaled.predict(X_test_scaled)))\n", "print(\"Recall:\", recall_score(y_test, logreg_scaled.predict(X_test_scaled)))\n", "print(\"F-1 score:\", f1_score(y_test, logreg_scaled.predict(X_test_scaled)))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "pXzCVQPmcvV8", "executionInfo": {"status": "ok", "timestamp": 1708934774417, "user_tz": -330, "elapsed": 547, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}, "outputId": "db3f1b81-19e6-4583-a033-00893193f2e9"}, "execution_count": 35, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Logistic Regression Classifier on scaled test data:\n", "Accuracy: 1.0\n", "Precision: 1.0\n", "Recall: 1.0\n", "F-1 score: 1.0\n"]}]}, {"cell_type": "code", "source": ["'''\n", "Well! The performance has definitely improved by feature scaling, though not drastically,\n", "as there was already very little scope of improvement.\n", "Let us look at other classifiers and analyse how they react to scaling.\n", "'''\n", "\n", "#SVM Classifier with RBF kernel: Using normal data\n", "svc_clf = SVC(C = 0.1, kernel = 'rbf').fit(X_train, y_train)\n", "print(\"SVM Classifier on unscaled test data:\")\n", "print(\"Accuracy:\", svc_clf.score(X_test, y_test))\n", "print(\"Precision:\", precision_score(y_test, svc_clf.predict(X_test)))\n", "print(\"Recall:\", recall_score(y_test, svc_clf.predict(X_test)))\n", "print(\"F-1 score:\", f1_score(y_test, svc_clf.predict(X_test)))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "dKZiYBnyczmb", "executionInfo": {"status": "ok", "timestamp": 1708934796138, "user_tz": -330, "elapsed": 681, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}, "outputId": "34deacc7-15c8-439e-9f7f-08c4e124129b"}, "execution_count": 36, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["SVM Classifier on unscaled test data:\n", "Accuracy: 0.7112676056338029\n", "Precision: 0.7112676056338029\n", "Recall: 1.0\n", "F-1 score: 0.831275720164609\n"]}]}, {"cell_type": "code", "source": ["#Using scaled data\n", "svc_clf_scaled = SVC(C = 0.1, kernel = 'rbf').fit(X_train_scaled, y_train)\n", "print(\"SVM Classifier on scaled test data:\")\n", "print(\"Accuracy:\", svc_clf_scaled.score(X_test_scaled, y_test))\n", "print(\"Precision:\", precision_score(y_test, svc_clf_scaled.predict(X_test_scaled)))\n", "print(\"Recall:\", recall_score(y_test, svc_clf_scaled.predict(X_test_scaled)))\n", "print(\"F-1 score:\", f1_score(y_test, svc_clf_scaled.predict(X_test_scaled)))\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "05MSC_-Ic43p", "executionInfo": {"status": "ok", "timestamp": 1708934814067, "user_tz": -330, "elapsed": 672, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}, "outputId": "48ef5258-c916-4b8d-8cc3-a5643ba216eb"}, "execution_count": 37, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["SVM Classifier on scaled test data:\n", "Accuracy: 1.0\n", "Precision: 1.0\n", "Recall: 1.0\n", "F-1 score: 1.0\n"]}]}, {"cell_type": "code", "source": ["#Random Forest Classifier: using normal data\n", "rfc = RandomForestClassifier(n_estimators = 20)\n", "rfc.fit(X_train, y_train)\n", "print(\"SVM Classifier on unscaled test data:\")\n", "print(\"Accuracy:\", rfc.score(X_test, y_test))\n", "print(\"Precision:\", precision_score(y_test, rfc.predict(X_test)))\n", "print(\"Recall:\", recall_score(y_test, rfc.predict(X_test)))\n", "print(\"F-1 score:\", f1_score(y_test, rfc.predict(X_test)))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "6SFwu8ATc9Pe", "executionInfo": {"status": "ok", "timestamp": 1708934827710, "user_tz": -330, "elapsed": 11, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}, "outputId": "8874fd8e-2c51-4a3e-e059-dcc7fd88b4f4"}, "execution_count": 38, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["SVM Classifier on unscaled test data:\n", "Accuracy: 1.0\n", "Precision: 1.0\n", "Recall: 1.0\n", "F-1 score: 1.0\n"]}]}, {"cell_type": "code", "source": ["#using scaled data\n", "rfc_scaled = RandomForestClassifier(n_estimators = 20)\n", "rfc_scaled.fit(X_train_scaled, y_train)\n", "print(\"Random Forest Classifier on scaled test data:\")\n", "print(\"Accuracy:\", rfc_scaled.score(X_test_scaled, y_test))\n", "print(\"Precision:\", precision_score(y_test, rfc_scaled.predict(X_test_scaled)))\n", "print(\"Recall:\", recall_score(y_test, rfc_scaled.predict(X_test_scaled)))\n", "print(\"F-1 score:\", f1_score(y_test, rfc_scaled.predict(X_test_scaled)))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "pa6PddDpdAqD", "executionInfo": {"status": "ok", "timestamp": 1708934844060, "user_tz": -330, "elapsed": 437, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}, "outputId": "52cc32f5-4b88-46b6-d904-fdcee1329b2a"}, "execution_count": 39, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Random Forest Classifier on scaled test data:\n", "Accuracy: 1.0\n", "Precision: 1.0\n", "Recall: 1.0\n", "F-1 score: 1.0\n"]}]}, {"cell_type": "code", "source": [], "metadata": {"id": "Iig8iLQ9dT2O"}, "execution_count": null, "outputs": []}]}