{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "authorship_tag": "ABX9TyMQnXxEHS2gZgtoZxPiMHMP"}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "code", "execution_count": null, "metadata": {"id": "idxBZPRMKMDP"}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn import svm\n", "from sklearn.linear_model import LogisticRegression\n", "from xgboost import XGBRegressor\n", "from sklearn.tree import DecisionTreeClassifier\n", "from sklearn import metrics\n", "from sklearn.metrics import accuracy_score, classification_report, confusion_matrix, mean_absolute_error, mean_squared_error, median_absolute_error"]}, {"cell_type": "code", "source": ["data = pd.read_csv(\"parkinsons.csv\")"], "metadata": {"id": "2bK7-UXMKqjg"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["data.head()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 255}, "id": "CkLOC2bbKwJQ", "executionInfo": {"status": "ok", "timestamp": 1708862945939, "user_tz": -330, "elapsed": 22, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}, "outputId": "e6868627-dd95-4b67-afa0-2c6fa7368873"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["             name  MDVP:Fo(Hz)  MDVP:Fhi(Hz)  MDVP:Flo(Hz)  MDVP:Jitter(%)  \\\n", "0  phon_R01_S01_1      119.992       157.302        74.997         0.00784   \n", "1  phon_R01_S01_2      122.400       148.650       113.819         0.00968   \n", "2  phon_R01_S01_3      116.682       131.111       111.555         0.01050   \n", "3  phon_R01_S01_4      116.676       137.871       111.366         0.00997   \n", "4  phon_R01_S01_5      116.014       141.781       110.655         0.01284   \n", "\n", "   MDVP:Jitter(Abs)  MDVP:RAP  MDVP:PPQ  Jitter:DDP  MDVP:Shimmer  ...  \\\n", "0           0.00007   0.00370   0.00554     0.01109       0.04374  ...   \n", "1           0.00008   0.00465   0.00696     0.01394       0.06134  ...   \n", "2           0.00009   0.00544   0.00781     0.01633       0.05233  ...   \n", "3           0.00009   0.00502   0.00698     0.01505       0.05492  ...   \n", "4           0.00011   0.00655   0.00908     0.01966       0.06425  ...   \n", "\n", "   Shimmer:DDA      NHR     HNR  status      RPDE       DFA   spread1  \\\n", "0      0.06545  0.02211  21.033       1  0.414783  0.815285 -4.813031   \n", "1      0.09403  0.01929  19.085       1  0.458359  0.819521 -4.075192   \n", "2      0.08270  0.01309  20.651       1  0.429895  0.825288 -4.443179   \n", "3      0.08771  0.01353  20.644       1  0.434969  0.819235 -4.117501   \n", "4      0.10470  0.01767  19.649       1  0.417356  0.823484 -3.747787   \n", "\n", "    spread2        D2       PPE  \n", "0  0.266482  2.301442  0.284654  \n", "1  0.335590  2.486855  0.368674  \n", "2  0.311173  2.342259  0.332634  \n", "3  0.334147  2.405554  0.368975  \n", "4  0.234513  2.332180  0.410335  \n", "\n", "[5 rows x 24 columns]"], "text/html": ["\n", "  <div id=\"df-827d3495-2d58-4c9a-8f9a-8e6a1cab43bd\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>MDVP:Fo(Hz)</th>\n", "      <th>MDVP:Fhi(Hz)</th>\n", "      <th>MDVP:Flo(Hz)</th>\n", "      <th>MDVP:<PERSON><PERSON>(%)</th>\n", "      <th>MDVP:<PERSON><PERSON>(Abs)</th>\n", "      <th>MDVP:RAP</th>\n", "      <th>MDVP:PPQ</th>\n", "      <th>Jitter:DDP</th>\n", "      <th>MDVP:<PERSON><PERSON></th>\n", "      <th>...</th>\n", "      <th>Shimmer:DDA</th>\n", "      <th>NHR</th>\n", "      <th>HNR</th>\n", "      <th>status</th>\n", "      <th>RPDE</th>\n", "      <th>DFA</th>\n", "      <th>spread1</th>\n", "      <th>spread2</th>\n", "      <th>D2</th>\n", "      <th>PPE</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>phon_R01_S01_1</td>\n", "      <td>119.992</td>\n", "      <td>157.302</td>\n", "      <td>74.997</td>\n", "      <td>0.00784</td>\n", "      <td>0.00007</td>\n", "      <td>0.00370</td>\n", "      <td>0.00554</td>\n", "      <td>0.01109</td>\n", "      <td>0.04374</td>\n", "      <td>...</td>\n", "      <td>0.06545</td>\n", "      <td>0.02211</td>\n", "      <td>21.033</td>\n", "      <td>1</td>\n", "      <td>0.414783</td>\n", "      <td>0.815285</td>\n", "      <td>-4.813031</td>\n", "      <td>0.266482</td>\n", "      <td>2.301442</td>\n", "      <td>0.284654</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>phon_R01_S01_2</td>\n", "      <td>122.400</td>\n", "      <td>148.650</td>\n", "      <td>113.819</td>\n", "      <td>0.00968</td>\n", "      <td>0.00008</td>\n", "      <td>0.00465</td>\n", "      <td>0.00696</td>\n", "      <td>0.01394</td>\n", "      <td>0.06134</td>\n", "      <td>...</td>\n", "      <td>0.09403</td>\n", "      <td>0.01929</td>\n", "      <td>19.085</td>\n", "      <td>1</td>\n", "      <td>0.458359</td>\n", "      <td>0.819521</td>\n", "      <td>-4.075192</td>\n", "      <td>0.335590</td>\n", "      <td>2.486855</td>\n", "      <td>0.368674</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>phon_R01_S01_3</td>\n", "      <td>116.682</td>\n", "      <td>131.111</td>\n", "      <td>111.555</td>\n", "      <td>0.01050</td>\n", "      <td>0.00009</td>\n", "      <td>0.00544</td>\n", "      <td>0.00781</td>\n", "      <td>0.01633</td>\n", "      <td>0.05233</td>\n", "      <td>...</td>\n", "      <td>0.08270</td>\n", "      <td>0.01309</td>\n", "      <td>20.651</td>\n", "      <td>1</td>\n", "      <td>0.429895</td>\n", "      <td>0.825288</td>\n", "      <td>-4.443179</td>\n", "      <td>0.311173</td>\n", "      <td>2.342259</td>\n", "      <td>0.332634</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>phon_R01_S01_4</td>\n", "      <td>116.676</td>\n", "      <td>137.871</td>\n", "      <td>111.366</td>\n", "      <td>0.00997</td>\n", "      <td>0.00009</td>\n", "      <td>0.00502</td>\n", "      <td>0.00698</td>\n", "      <td>0.01505</td>\n", "      <td>0.05492</td>\n", "      <td>...</td>\n", "      <td>0.08771</td>\n", "      <td>0.01353</td>\n", "      <td>20.644</td>\n", "      <td>1</td>\n", "      <td>0.434969</td>\n", "      <td>0.819235</td>\n", "      <td>-4.117501</td>\n", "      <td>0.334147</td>\n", "      <td>2.405554</td>\n", "      <td>0.368975</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>phon_R01_S01_5</td>\n", "      <td>116.014</td>\n", "      <td>141.781</td>\n", "      <td>110.655</td>\n", "      <td>0.01284</td>\n", "      <td>0.00011</td>\n", "      <td>0.00655</td>\n", "      <td>0.00908</td>\n", "      <td>0.01966</td>\n", "      <td>0.06425</td>\n", "      <td>...</td>\n", "      <td>0.10470</td>\n", "      <td>0.01767</td>\n", "      <td>19.649</td>\n", "      <td>1</td>\n", "      <td>0.417356</td>\n", "      <td>0.823484</td>\n", "      <td>-3.747787</td>\n", "      <td>0.234513</td>\n", "      <td>2.332180</td>\n", "      <td>0.410335</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 24 columns</p>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-827d3495-2d58-4c9a-8f9a-8e6a1cab43bd')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-827d3495-2d58-4c9a-8f9a-8e6a1cab43bd button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-827d3495-2d58-4c9a-8f9a-8e6a1cab43bd');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-419a0004-4bc6-498d-95ee-4ddd8eb1ecd6\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-419a0004-4bc6-498d-95ee-4ddd8eb1ecd6')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-419a0004-4bc6-498d-95ee-4ddd8eb1ecd6 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "data"}}, "metadata": {}, "execution_count": 3}]}, {"cell_type": "code", "source": ["data.shape"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "qteAojZlKzSK", "executionInfo": {"status": "ok", "timestamp": 1708862963944, "user_tz": -330, "elapsed": 13, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}, "outputId": "0d4c4400-59d8-46ae-f2be-2e765d265190"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["(195, 24)"]}, "metadata": {}, "execution_count": 4}]}, {"cell_type": "code", "source": ["data.info()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "SVUvX67hK3xT", "executionInfo": {"status": "ok", "timestamp": 1708862978621, "user_tz": -330, "elapsed": 15, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}, "outputId": "93c41b8e-5282-4920-af5b-9d15315f3ae8"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 195 entries, 0 to 194\n", "Data columns (total 24 columns):\n", " #   Column            Non-Null Count  Dtype  \n", "---  ------            --------------  -----  \n", " 0   name              195 non-null    object \n", " 1   MDVP:Fo(Hz)       195 non-null    float64\n", " 2   MDVP:Fhi(Hz)      195 non-null    float64\n", " 3   MDVP:Flo(Hz)      195 non-null    float64\n", " 4   MDVP:<PERSON><PERSON>(%)    195 non-null    float64\n", " 5   MDVP:<PERSON><PERSON>(Abs)  195 non-null    float64\n", " 6   MDVP:RAP          195 non-null    float64\n", " 7   MDVP:PPQ          195 non-null    float64\n", " 8   Jitter:DDP        195 non-null    float64\n", " 9   MDVP:<PERSON><PERSON>      195 non-null    float64\n", " 10  MDVP:Shimmer(dB)  195 non-null    float64\n", " 11  Shimmer:APQ3      195 non-null    float64\n", " 12  Shimmer:APQ5      195 non-null    float64\n", " 13  MDVP:APQ          195 non-null    float64\n", " 14  Shimmer:DDA       195 non-null    float64\n", " 15  NHR               195 non-null    float64\n", " 16  HNR               195 non-null    float64\n", " 17  status            195 non-null    int64  \n", " 18  RPDE              195 non-null    float64\n", " 19  DFA               195 non-null    float64\n", " 20  spread1           195 non-null    float64\n", " 21  spread2           195 non-null    float64\n", " 22  D2                195 non-null    float64\n", " 23  PPE               195 non-null    float64\n", "dtypes: float64(22), int64(1), object(1)\n", "memory usage: 36.7+ KB\n"]}]}, {"cell_type": "code", "source": ["data.isnull().sum()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "x6jjTu7PK7OJ", "executionInfo": {"status": "ok", "timestamp": 1708862993350, "user_tz": -330, "elapsed": 11, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}, "outputId": "193050ae-0ff0-49d8-f897-bd7b42923f8b"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["name                0\n", "MDVP:Fo(Hz)         0\n", "MDVP:Fhi(Hz)        0\n", "MDVP:Flo(Hz)        0\n", "MDVP:<PERSON><PERSON>(%)      0\n", "MDVP:<PERSON><PERSON>(Abs)    0\n", "MDVP:RAP            0\n", "MDVP:PPQ            0\n", "Jitter:DDP          0\n", "MDVP:<PERSON><PERSON>        0\n", "MDVP:<PERSON><PERSON>(dB)    0\n", "Shimmer:APQ3        0\n", "Shimmer:APQ5        0\n", "MDVP:APQ            0\n", "Shimmer:DDA         0\n", "NHR                 0\n", "HNR                 0\n", "status              0\n", "RPDE                0\n", "DFA                 0\n", "spread1             0\n", "spread2             0\n", "D2                  0\n", "PPE                 0\n", "dtype: int64"]}, "metadata": {}, "execution_count": 6}]}, {"cell_type": "code", "source": ["data.describe()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 349}, "id": "bed9qRU6K-82", "executionInfo": {"status": "ok", "timestamp": 1708863009052, "user_tz": -330, "elapsed": 19, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}, "outputId": "8aab17c3-c2d9-4cdd-fc35-ab0e70cae11a"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["       MDVP:Fo(Hz)  MDVP:Fhi(Hz)  MDVP:Flo(Hz)  MDVP:Jitter(%)  \\\n", "count   195.000000    195.000000    195.000000      195.000000   \n", "mean    154.228641    197.104918    116.324631        0.006220   \n", "std      41.390065     91.491548     43.521413        0.004848   \n", "min      88.333000    102.145000     65.476000        0.001680   \n", "25%     117.572000    134.862500     84.291000        0.003460   \n", "50%     148.790000    175.829000    104.315000        0.004940   \n", "75%     182.769000    224.205500    140.018500        0.007365   \n", "max     260.105000    592.030000    239.170000        0.033160   \n", "\n", "       MDVP:Jitter(Abs)    MDVP:RAP    MDVP:PPQ  Jitter:DDP  MDVP:<PERSON><PERSON>  \\\n", "count        195.000000  195.000000  195.000000  195.000000    195.000000   \n", "mean           0.000044    0.003306    0.003446    0.009920      0.029709   \n", "std            0.000035    0.002968    0.002759    0.008903      0.018857   \n", "min            0.000007    0.000680    0.000920    0.002040      0.009540   \n", "25%            0.000020    0.001660    0.001860    0.004985      0.016505   \n", "50%            0.000030    0.002500    0.002690    0.007490      0.022970   \n", "75%            0.000060    0.003835    0.003955    0.011505      0.037885   \n", "max            0.000260    0.021440    0.019580    0.064330      0.119080   \n", "\n", "       MDVP:Shimmer(dB)  ...  Shimmer:DDA         NHR         HNR      status  \\\n", "count        195.000000  ...   195.000000  195.000000  195.000000  195.000000   \n", "mean           0.282251  ...     0.046993    0.024847   21.885974    0.753846   \n", "std            0.194877  ...     0.030459    0.040418    4.425764    0.431878   \n", "min            0.085000  ...     0.013640    0.000650    8.441000    0.000000   \n", "25%            0.148500  ...     0.024735    0.005925   19.198000    1.000000   \n", "50%            0.221000  ...     0.038360    0.011660   22.085000    1.000000   \n", "75%            0.350000  ...     0.060795    0.025640   25.075500    1.000000   \n", "max            1.302000  ...     0.169420    0.314820   33.047000    1.000000   \n", "\n", "             RPDE         DFA     spread1     spread2          D2         PPE  \n", "count  195.000000  195.000000  195.000000  195.000000  195.000000  195.000000  \n", "mean     0.498536    0.718099   -5.684397    0.226510    2.381826    0.206552  \n", "std      0.103942    0.055336    1.090208    0.083406    0.382799    0.090119  \n", "min      0.256570    0.574282   -7.964984    0.006274    1.423287    0.044539  \n", "25%      0.421306    0.674758   -6.450096    0.174351    2.099125    0.137451  \n", "50%      0.495954    0.722254   -5.720868    0.218885    2.361532    0.194052  \n", "75%      0.587562    0.761881   -5.046192    0.279234    2.636456    0.252980  \n", "max      0.685151    0.825288   -2.434031    0.450493    3.671155    0.527367  \n", "\n", "[8 rows x 23 columns]"], "text/html": ["\n", "  <div id=\"df-2650744a-725b-4152-aed5-a6d0be559d57\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>MDVP:Fo(Hz)</th>\n", "      <th>MDVP:Fhi(Hz)</th>\n", "      <th>MDVP:Flo(Hz)</th>\n", "      <th>MDVP:<PERSON><PERSON>(%)</th>\n", "      <th>MDVP:<PERSON><PERSON>(Abs)</th>\n", "      <th>MDVP:RAP</th>\n", "      <th>MDVP:PPQ</th>\n", "      <th>Jitter:DDP</th>\n", "      <th>MDVP:<PERSON><PERSON></th>\n", "      <th>MDVP:<PERSON><PERSON>(dB)</th>\n", "      <th>...</th>\n", "      <th>Shimmer:DDA</th>\n", "      <th>NHR</th>\n", "      <th>HNR</th>\n", "      <th>status</th>\n", "      <th>RPDE</th>\n", "      <th>DFA</th>\n", "      <th>spread1</th>\n", "      <th>spread2</th>\n", "      <th>D2</th>\n", "      <th>PPE</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>195.000000</td>\n", "      <td>195.000000</td>\n", "      <td>195.000000</td>\n", "      <td>195.000000</td>\n", "      <td>195.000000</td>\n", "      <td>195.000000</td>\n", "      <td>195.000000</td>\n", "      <td>195.000000</td>\n", "      <td>195.000000</td>\n", "      <td>195.000000</td>\n", "      <td>...</td>\n", "      <td>195.000000</td>\n", "      <td>195.000000</td>\n", "      <td>195.000000</td>\n", "      <td>195.000000</td>\n", "      <td>195.000000</td>\n", "      <td>195.000000</td>\n", "      <td>195.000000</td>\n", "      <td>195.000000</td>\n", "      <td>195.000000</td>\n", "      <td>195.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>154.228641</td>\n", "      <td>197.104918</td>\n", "      <td>116.324631</td>\n", "      <td>0.006220</td>\n", "      <td>0.000044</td>\n", "      <td>0.003306</td>\n", "      <td>0.003446</td>\n", "      <td>0.009920</td>\n", "      <td>0.029709</td>\n", "      <td>0.282251</td>\n", "      <td>...</td>\n", "      <td>0.046993</td>\n", "      <td>0.024847</td>\n", "      <td>21.885974</td>\n", "      <td>0.753846</td>\n", "      <td>0.498536</td>\n", "      <td>0.718099</td>\n", "      <td>-5.684397</td>\n", "      <td>0.226510</td>\n", "      <td>2.381826</td>\n", "      <td>0.206552</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>41.390065</td>\n", "      <td>91.491548</td>\n", "      <td>43.521413</td>\n", "      <td>0.004848</td>\n", "      <td>0.000035</td>\n", "      <td>0.002968</td>\n", "      <td>0.002759</td>\n", "      <td>0.008903</td>\n", "      <td>0.018857</td>\n", "      <td>0.194877</td>\n", "      <td>...</td>\n", "      <td>0.030459</td>\n", "      <td>0.040418</td>\n", "      <td>4.425764</td>\n", "      <td>0.431878</td>\n", "      <td>0.103942</td>\n", "      <td>0.055336</td>\n", "      <td>1.090208</td>\n", "      <td>0.083406</td>\n", "      <td>0.382799</td>\n", "      <td>0.090119</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>88.333000</td>\n", "      <td>102.145000</td>\n", "      <td>65.476000</td>\n", "      <td>0.001680</td>\n", "      <td>0.000007</td>\n", "      <td>0.000680</td>\n", "      <td>0.000920</td>\n", "      <td>0.002040</td>\n", "      <td>0.009540</td>\n", "      <td>0.085000</td>\n", "      <td>...</td>\n", "      <td>0.013640</td>\n", "      <td>0.000650</td>\n", "      <td>8.441000</td>\n", "      <td>0.000000</td>\n", "      <td>0.256570</td>\n", "      <td>0.574282</td>\n", "      <td>-7.964984</td>\n", "      <td>0.006274</td>\n", "      <td>1.423287</td>\n", "      <td>0.044539</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>117.572000</td>\n", "      <td>134.862500</td>\n", "      <td>84.291000</td>\n", "      <td>0.003460</td>\n", "      <td>0.000020</td>\n", "      <td>0.001660</td>\n", "      <td>0.001860</td>\n", "      <td>0.004985</td>\n", "      <td>0.016505</td>\n", "      <td>0.148500</td>\n", "      <td>...</td>\n", "      <td>0.024735</td>\n", "      <td>0.005925</td>\n", "      <td>19.198000</td>\n", "      <td>1.000000</td>\n", "      <td>0.421306</td>\n", "      <td>0.674758</td>\n", "      <td>-6.450096</td>\n", "      <td>0.174351</td>\n", "      <td>2.099125</td>\n", "      <td>0.137451</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>148.790000</td>\n", "      <td>175.829000</td>\n", "      <td>104.315000</td>\n", "      <td>0.004940</td>\n", "      <td>0.000030</td>\n", "      <td>0.002500</td>\n", "      <td>0.002690</td>\n", "      <td>0.007490</td>\n", "      <td>0.022970</td>\n", "      <td>0.221000</td>\n", "      <td>...</td>\n", "      <td>0.038360</td>\n", "      <td>0.011660</td>\n", "      <td>22.085000</td>\n", "      <td>1.000000</td>\n", "      <td>0.495954</td>\n", "      <td>0.722254</td>\n", "      <td>-5.720868</td>\n", "      <td>0.218885</td>\n", "      <td>2.361532</td>\n", "      <td>0.194052</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>182.769000</td>\n", "      <td>224.205500</td>\n", "      <td>140.018500</td>\n", "      <td>0.007365</td>\n", "      <td>0.000060</td>\n", "      <td>0.003835</td>\n", "      <td>0.003955</td>\n", "      <td>0.011505</td>\n", "      <td>0.037885</td>\n", "      <td>0.350000</td>\n", "      <td>...</td>\n", "      <td>0.060795</td>\n", "      <td>0.025640</td>\n", "      <td>25.075500</td>\n", "      <td>1.000000</td>\n", "      <td>0.587562</td>\n", "      <td>0.761881</td>\n", "      <td>-5.046192</td>\n", "      <td>0.279234</td>\n", "      <td>2.636456</td>\n", "      <td>0.252980</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>260.105000</td>\n", "      <td>592.030000</td>\n", "      <td>239.170000</td>\n", "      <td>0.033160</td>\n", "      <td>0.000260</td>\n", "      <td>0.021440</td>\n", "      <td>0.019580</td>\n", "      <td>0.064330</td>\n", "      <td>0.119080</td>\n", "      <td>1.302000</td>\n", "      <td>...</td>\n", "      <td>0.169420</td>\n", "      <td>0.314820</td>\n", "      <td>33.047000</td>\n", "      <td>1.000000</td>\n", "      <td>0.685151</td>\n", "      <td>0.825288</td>\n", "      <td>-2.434031</td>\n", "      <td>0.450493</td>\n", "      <td>3.671155</td>\n", "      <td>0.527367</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>8 rows × 23 columns</p>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-2650744a-725b-4152-aed5-a6d0be559d57')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-2650744a-725b-4152-aed5-a6d0be559d57 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-2650744a-725b-4152-aed5-a6d0be559d57');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-7c92ae8a-b024-4367-a447-aa22613ceb4c\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-7c92ae8a-b024-4367-a447-aa22613ceb4c')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-7c92ae8a-b024-4367-a447-aa22613ceb4c button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe"}}, "metadata": {}, "execution_count": 7}]}, {"cell_type": "code", "source": ["data[\"status\"].value_counts()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "n2tW-12JLCxk", "executionInfo": {"status": "ok", "timestamp": 1708863024580, "user_tz": -330, "elapsed": 13, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}, "outputId": "3ee7e596-4d64-4eac-fd20-c258c73db92d"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["1    147\n", "0     48\n", "Name: status, dtype: int64"]}, "metadata": {}, "execution_count": 8}]}, {"cell_type": "code", "source": ["sns.countplot(x = \"status\", data = data)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 466}, "id": "XkN290HuLGiN", "executionInfo": {"status": "ok", "timestamp": 1708863044089, "user_tz": -330, "elapsed": 1038, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}, "outputId": "fbbacb8d-c2a2-47e5-843c-3d05654f6a67"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["<Axes: xlabel='status', ylabel='count'>"]}, "metadata": {}, "execution_count": 9}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 640x480 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}]}, {"cell_type": "code", "source": ["sns.catplot(x = \"status\", y = \"MDVP:Fo(Hz)\", data = data)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 523}, "id": "g9P82IYULLJK", "executionInfo": {"status": "ok", "timestamp": 1708863069333, "user_tz": -330, "elapsed": 1299, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}, "outputId": "e7ccda01-497d-491b-df18-145d08c575f3"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["<seaborn.axisgrid.FacetGrid at 0x79eaacfdad10>"]}, "metadata": {}, "execution_count": 10}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 511.111x500 with 1 Axes>"], "image/png": "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********************************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\n"}, "metadata": {}}]}, {"cell_type": "code", "source": ["sns.catplot(x = \"status\", y = \"MDVP:Fhi(Hz)\", data = data)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 523}, "id": "WWdS3OUcLRME", "executionInfo": {"status": "ok", "timestamp": 1708863085564, "user_tz": -330, "elapsed": 1519, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}, "outputId": "b7c9e023-31f3-4116-b12f-9dbdb8f16a23"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["<seaborn.axisgrid.FacetGrid at 0x79eaaad82ef0>"]}, "metadata": {}, "execution_count": 11}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 511.111x500 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}]}, {"cell_type": "code", "source": ["data.groupby(\"status\").mean()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 227}, "id": "X7PvcRCsLVK5", "executionInfo": {"status": "ok", "timestamp": 1708863100534, "user_tz": -330, "elapsed": 568, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}, "outputId": "37217dce-f2d5-43c2-aa49-70bc47158b55"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["<ipython-input-12-4991dba114fd>:1: FutureWarning: The default value of numeric_only in DataFrameGroupBy.mean is deprecated. In a future version, numeric_only will default to False. Either specify numeric_only or select only columns which should be valid for the function.\n", "  data.groupby(\"status\").mean()\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["        MDVP:Fo(Hz)  MDVP:Fhi(Hz)  MDVP:Flo(Hz)  MDVP:Jitter(%)  \\\n", "status                                                            \n", "0        181.937771    223.636750    145.207292        0.003866   \n", "1        145.180762    188.441463    106.893558        0.006989   \n", "\n", "        MDVP:Jitter(Abs)  MDVP:RAP  MDVP:PPQ  Jitter:DDP  MDVP:<PERSON><PERSON>  \\\n", "status                                                                   \n", "0               0.000023  0.001925  0.002056    0.005776      0.017615   \n", "1               0.000051  0.003757  0.003900    0.011273      0.033658   \n", "\n", "        MDVP:Shimmer(dB)  ...  MDVP:APQ  Shimmer:DDA       NHR        HNR  \\\n", "status                    ...                                               \n", "0               0.162958  ...  0.013305     0.028511  0.011483  24.678750   \n", "1               0.321204  ...  0.027600     0.053027  0.029211  20.974048   \n", "\n", "            RPDE       DFA   spread1   spread2        D2       PPE  \n", "status                                                              \n", "0       0.442552  0.695716 -6.759264  0.160292  2.154491  0.123017  \n", "1       0.516816  0.725408 -5.333420  0.248133  2.456058  0.233828  \n", "\n", "[2 rows x 22 columns]"], "text/html": ["\n", "  <div id=\"df-9a7f241c-f167-4a9b-9373-b09dbc6bac03\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>MDVP:Fo(Hz)</th>\n", "      <th>MDVP:Fhi(Hz)</th>\n", "      <th>MDVP:Flo(Hz)</th>\n", "      <th>MDVP:<PERSON><PERSON>(%)</th>\n", "      <th>MDVP:<PERSON><PERSON>(Abs)</th>\n", "      <th>MDVP:RAP</th>\n", "      <th>MDVP:PPQ</th>\n", "      <th>Jitter:DDP</th>\n", "      <th>MDVP:<PERSON><PERSON></th>\n", "      <th>MDVP:<PERSON><PERSON>(dB)</th>\n", "      <th>...</th>\n", "      <th>MDVP:APQ</th>\n", "      <th>Shimmer:DDA</th>\n", "      <th>NHR</th>\n", "      <th>HNR</th>\n", "      <th>RPDE</th>\n", "      <th>DFA</th>\n", "      <th>spread1</th>\n", "      <th>spread2</th>\n", "      <th>D2</th>\n", "      <th>PPE</th>\n", "    </tr>\n", "    <tr>\n", "      <th>status</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>181.937771</td>\n", "      <td>223.636750</td>\n", "      <td>145.207292</td>\n", "      <td>0.003866</td>\n", "      <td>0.000023</td>\n", "      <td>0.001925</td>\n", "      <td>0.002056</td>\n", "      <td>0.005776</td>\n", "      <td>0.017615</td>\n", "      <td>0.162958</td>\n", "      <td>...</td>\n", "      <td>0.013305</td>\n", "      <td>0.028511</td>\n", "      <td>0.011483</td>\n", "      <td>24.678750</td>\n", "      <td>0.442552</td>\n", "      <td>0.695716</td>\n", "      <td>-6.759264</td>\n", "      <td>0.160292</td>\n", "      <td>2.154491</td>\n", "      <td>0.123017</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>145.180762</td>\n", "      <td>188.441463</td>\n", "      <td>106.893558</td>\n", "      <td>0.006989</td>\n", "      <td>0.000051</td>\n", "      <td>0.003757</td>\n", "      <td>0.003900</td>\n", "      <td>0.011273</td>\n", "      <td>0.033658</td>\n", "      <td>0.321204</td>\n", "      <td>...</td>\n", "      <td>0.027600</td>\n", "      <td>0.053027</td>\n", "      <td>0.029211</td>\n", "      <td>20.974048</td>\n", "      <td>0.516816</td>\n", "      <td>0.725408</td>\n", "      <td>-5.333420</td>\n", "      <td>0.248133</td>\n", "      <td>2.456058</td>\n", "      <td>0.233828</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>2 rows × 22 columns</p>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-9a7f241c-f167-4a9b-9373-b09dbc6bac03')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-9a7f241c-f167-4a9b-9373-b09dbc6bac03 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-9a7f241c-f167-4a9b-9373-b09dbc6bac03');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-dfcc5a58-6ec6-45ad-826c-70e03e71759f\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-dfcc5a58-6ec6-45ad-826c-70e03e71759f')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-dfcc5a58-6ec6-45ad-826c-70e03e71759f button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe"}}, "metadata": {}, "execution_count": 12}]}, {"cell_type": "code", "source": ["x = data.drop(columns = [\"name\",\"status\"], axis = 1)\n", "y = data[\"status\"]"], "metadata": {"id": "PirARkPyLY-t"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["print(x)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "SJi0O4Y9LcpW", "executionInfo": {"status": "ok", "timestamp": 1708863126790, "user_tz": -330, "elapsed": 454, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}, "outputId": "b6190fcc-17fd-47d3-ec0c-5653fcf539d5"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["     MDVP:Fo(Hz)  MDVP:Fhi(Hz)  MDVP:Flo(Hz)  MDVP:Jitter(%)  \\\n", "0        119.992       157.302        74.997         0.00784   \n", "1        122.400       148.650       113.819         0.00968   \n", "2        116.682       131.111       111.555         0.01050   \n", "3        116.676       137.871       111.366         0.00997   \n", "4        116.014       141.781       110.655         0.01284   \n", "..           ...           ...           ...             ...   \n", "190      174.188       230.978        94.261         0.00459   \n", "191      209.516       253.017        89.488         0.00564   \n", "192      174.688       240.005        74.287         0.01360   \n", "193      198.764       396.961        74.904         0.00740   \n", "194      214.289       260.277        77.973         0.00567   \n", "\n", "     MDVP:Jitter(Abs)  MDVP:RAP  MDVP:PPQ  Jitter:DDP  MDVP:<PERSON><PERSON>  \\\n", "0             0.00007   0.00370   0.00554     0.01109       0.04374   \n", "1             0.00008   0.00465   0.00696     0.01394       0.06134   \n", "2             0.00009   0.00544   0.00781     0.01633       0.05233   \n", "3             0.00009   0.00502   0.00698     0.01505       0.05492   \n", "4             0.00011   0.00655   0.00908     0.01966       0.06425   \n", "..                ...       ...       ...         ...           ...   \n", "190           0.00003   0.00263   0.00259     0.00790       0.04087   \n", "191           0.00003   0.00331   0.00292     0.00994       0.02751   \n", "192           0.00008   0.00624   0.00564     0.01873       0.02308   \n", "193           0.00004   0.00370   0.00390     0.01109       0.02296   \n", "194           0.00003   0.00295   0.00317     0.00885       0.01884   \n", "\n", "     MDVP:Shimmer(dB)  ...  MDVP:APQ  Shimmer:DDA      NHR     HNR      RPDE  \\\n", "0               0.426  ...   0.02971      0.06545  0.02211  21.033  0.414783   \n", "1               0.626  ...   0.04368      0.09403  0.01929  19.085  0.458359   \n", "2               0.482  ...   0.03590      0.08270  0.01309  20.651  0.429895   \n", "3               0.517  ...   0.03772      0.08771  0.01353  20.644  0.434969   \n", "4               0.584  ...   0.04465      0.10470  0.01767  19.649  0.417356   \n", "..                ...  ...       ...          ...      ...     ...       ...   \n", "190             0.405  ...   0.02745      0.07008  0.02764  19.517  0.448439   \n", "191             0.263  ...   0.01879      0.04812  0.01810  19.147  0.431674   \n", "192             0.256  ...   0.01667      0.03804  0.10715  17.883  0.407567   \n", "193             0.241  ...   0.01588      0.03794  0.07223  19.020  0.451221   \n", "194             0.190  ...   0.01373      0.03078  0.04398  21.209  0.462803   \n", "\n", "          DFA   spread1   spread2        D2       PPE  \n", "0    0.815285 -4.813031  0.266482  2.301442  0.284654  \n", "1    0.819521 -4.075192  0.335590  2.486855  0.368674  \n", "2    0.825288 -4.443179  0.311173  2.342259  0.332634  \n", "3    0.819235 -4.117501  0.334147  2.405554  0.368975  \n", "4    0.823484 -3.747787  0.234513  2.332180  0.410335  \n", "..        ...       ...       ...       ...       ...  \n", "190  0.657899 -6.538586  0.121952  2.657476  0.133050  \n", "191  0.683244 -6.195325  0.129303  2.784312  0.168895  \n", "192  0.655683 -6.787197  0.158453  2.679772  0.131728  \n", "193  0.643956 -6.744577  0.207454  2.138608  0.123306  \n", "194  0.664357 -5.724056  0.190667  2.555477  0.148569  \n", "\n", "[195 rows x 22 columns]\n"]}]}, {"cell_type": "code", "source": ["print(y)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "OFSeu1JDLfbS", "executionInfo": {"status": "ok", "timestamp": 1708863145083, "user_tz": -330, "elapsed": 10, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}, "outputId": "6da7225e-bd61-44f5-fe17-c470997dae1a"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["0      1\n", "1      1\n", "2      1\n", "3      1\n", "4      1\n", "      ..\n", "190    0\n", "191    0\n", "192    0\n", "193    0\n", "194    0\n", "Name: status, Length: 195, dtype: int64\n"]}]}, {"cell_type": "code", "source": ["x_train, x_test, y_train, y_test = train_test_split(x, y, test_size = 0.2, random_state = 5)"], "metadata": {"id": "3TbJSp5tLj_Q"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["print(x.shape, x_train.shape, x_test.shape)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "zIANepuxLoF0", "executionInfo": {"status": "ok", "timestamp": 1708863174946, "user_tz": -330, "elapsed": 480, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}, "outputId": "4bda8a92-f764-406b-c69c-ce718f0832f7"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["(195, 22) (156, 22) (39, 22)\n"]}]}, {"cell_type": "code", "source": ["scaler = StandardScaler()"], "metadata": {"id": "Tbpz0vbaLrOj"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["scaler.fit(x_train)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 74}, "id": "giNuMLGQLuGO", "executionInfo": {"status": "ok", "timestamp": 1708863197967, "user_tz": -330, "elapsed": 453, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}, "outputId": "1646c24e-8b0d-4bad-a134-f097ecf18598"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["StandardScaler()"], "text/html": ["<style>#sk-container-id-1 {color: black;background-color: white;}#sk-container-id-1 pre{padding: 0;}#sk-container-id-1 div.sk-toggleable {background-color: white;}#sk-container-id-1 label.sk-toggleable__label {cursor: pointer;display: block;width: 100%;margin-bottom: 0;padding: 0.3em;box-sizing: border-box;text-align: center;}#sk-container-id-1 label.sk-toggleable__label-arrow:before {content: \"▸\";float: left;margin-right: 0.25em;color: #696969;}#sk-container-id-1 label.sk-toggleable__label-arrow:hover:before {color: black;}#sk-container-id-1 div.sk-estimator:hover label.sk-toggleable__label-arrow:before {color: black;}#sk-container-id-1 div.sk-toggleable__content {max-height: 0;max-width: 0;overflow: hidden;text-align: left;background-color: #f0f8ff;}#sk-container-id-1 div.sk-toggleable__content pre {margin: 0.2em;color: black;border-radius: 0.25em;background-color: #f0f8ff;}#sk-container-id-1 input.sk-toggleable__control:checked~div.sk-toggleable__content {max-height: 200px;max-width: 100%;overflow: auto;}#sk-container-id-1 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {content: \"▾\";}#sk-container-id-1 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {background-color: #d4ebff;}#sk-container-id-1 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {background-color: #d4ebff;}#sk-container-id-1 input.sk-hidden--visually {border: 0;clip: rect(1px 1px 1px 1px);clip: rect(1px, 1px, 1px, 1px);height: 1px;margin: -1px;overflow: hidden;padding: 0;position: absolute;width: 1px;}#sk-container-id-1 div.sk-estimator {font-family: monospace;background-color: #f0f8ff;border: 1px dotted black;border-radius: 0.25em;box-sizing: border-box;margin-bottom: 0.5em;}#sk-container-id-1 div.sk-estimator:hover {background-color: #d4ebff;}#sk-container-id-1 div.sk-parallel-item::after {content: \"\";width: 100%;border-bottom: 1px solid gray;flex-grow: 1;}#sk-container-id-1 div.sk-label:hover label.sk-toggleable__label {background-color: #d4ebff;}#sk-container-id-1 div.sk-serial::before {content: \"\";position: absolute;border-left: 1px solid gray;box-sizing: border-box;top: 0;bottom: 0;left: 50%;z-index: 0;}#sk-container-id-1 div.sk-serial {display: flex;flex-direction: column;align-items: center;background-color: white;padding-right: 0.2em;padding-left: 0.2em;position: relative;}#sk-container-id-1 div.sk-item {position: relative;z-index: 1;}#sk-container-id-1 div.sk-parallel {display: flex;align-items: stretch;justify-content: center;background-color: white;position: relative;}#sk-container-id-1 div.sk-item::before, #sk-container-id-1 div.sk-parallel-item::before {content: \"\";position: absolute;border-left: 1px solid gray;box-sizing: border-box;top: 0;bottom: 0;left: 50%;z-index: -1;}#sk-container-id-1 div.sk-parallel-item {display: flex;flex-direction: column;z-index: 1;position: relative;background-color: white;}#sk-container-id-1 div.sk-parallel-item:first-child::after {align-self: flex-end;width: 50%;}#sk-container-id-1 div.sk-parallel-item:last-child::after {align-self: flex-start;width: 50%;}#sk-container-id-1 div.sk-parallel-item:only-child::after {width: 0;}#sk-container-id-1 div.sk-dashed-wrapped {border: 1px dashed gray;margin: 0 0.4em 0.5em 0.4em;box-sizing: border-box;padding-bottom: 0.4em;background-color: white;}#sk-container-id-1 div.sk-label label {font-family: monospace;font-weight: bold;display: inline-block;line-height: 1.2em;}#sk-container-id-1 div.sk-label-container {text-align: center;}#sk-container-id-1 div.sk-container {/* jupyter's `normalize.less` sets `[hidden] { display: none; }` but bootstrap.min.css set `[hidden] { display: none !important; }` so we also need the `!important` here to be able to override the default hidden behavior on the sphinx rendered scikit-learn.org. See: https://github.com/scikit-learn/scikit-learn/issues/21755 */display: inline-block !important;position: relative;}#sk-container-id-1 div.sk-text-repr-fallback {display: none;}</style><div id=\"sk-container-id-1\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>StandardScaler()</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item\"><div class=\"sk-estimator sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-1\" type=\"checkbox\" checked><label for=\"sk-estimator-id-1\" class=\"sk-toggleable__label sk-toggleable__label-arrow\">StandardScaler</label><div class=\"sk-toggleable__content\"><pre>StandardScaler()</pre></div></div></div></div></div>"]}, "metadata": {}, "execution_count": 19}]}, {"cell_type": "code", "source": ["x_train = scaler.transform(x_train)\n", "x_test = scaler.transform(x_test)"], "metadata": {"id": "-Vm6-nVsLw13"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["print(x_train)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "dAKsoqi1L0xT", "executionInfo": {"status": "ok", "timestamp": 1708863226663, "user_tz": -330, "elapsed": 467, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}, "outputId": "dd6cc45e-7bc8-49c3-d5de-e250f089e313"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["[[ 1.20771132e-01 -2.83526809e-01  6.66366786e-01 ... -3.60451119e-01\n", "   2.71884735e-01 -6.14338939e-01]\n", " [ 4.75621012e-01 -2.22724479e-03 -7.04803291e-01 ... -1.73169972e-01\n", "   4.73377677e-01  5.34369593e-01]\n", " [-7.59484810e-01 -5.22800298e-01 -5.84256113e-02 ...  1.32621591e+00\n", "   3.08825153e-01  1.83275864e+00]\n", " ...\n", " [ 5.80342604e-01  2.95092222e+00 -8.01085245e-01 ...  2.01381483e+00\n", "   1.20391948e+00  5.16504027e-01]\n", " [ 1.14348488e+00  8.24029706e-01 -8.90772223e-01 ... -7.76090347e-01\n", "  -1.53768392e-01 -1.05275587e+00]\n", " [-6.60900748e-01 -6.04207367e-01 -3.86981638e-01 ...  2.16683996e-01\n", "   7.09749145e-01  1.94339206e+00]]\n"]}]}, {"cell_type": "code", "source": ["print(x_test)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "J1w1F15-L3x4", "executionInfo": {"status": "ok", "timestamp": 1708863242597, "user_tz": -330, "elapsed": 514, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}, "outputId": "3061768c-7525-4f42-d314-b23a8cbb8a28"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["[[-1.32519238e+00 -1.07006032e+00 -6.66823721e-01 -3.91250615e-01\n", "  -1.13937924e-01 -2.89819778e-01 -3.14444354e-01 -2.88801733e-01\n", "  -4.05784386e-01 -4.16974665e-01 -3.00781520e-01 -3.81623433e-01\n", "  -4.98535701e-01 -3.00793796e-01 -4.03785808e-01  1.05901201e-01\n", "   7.52311278e-01  1.01714986e+00 -3.09788657e-01 -1.75351319e+00\n", "  -8.66599627e-01 -3.25985995e-01]\n", " [-8.10622895e-01 -6.57486841e-01 -3.50157897e-02 -5.94982180e-01\n", "  -4.03421899e-01 -6.00067667e-01 -5.16696296e-01 -5.99047428e-01\n", "  -7.28736878e-01 -7.31854347e-01 -7.92286184e-01 -7.11762914e-01\n", "  -6.12636849e-01 -7.92311628e-01 -4.72801874e-01  7.08100313e-01\n", "   9.42610515e-01  8.65966504e-01  5.05993299e-02  3.94021964e-01\n", "  -1.40599050e+00  6.29171530e-02]\n", " [-9.67705283e-01 -9.03113246e-01 -4.53796683e-01 -5.62055866e-01\n", "  -4.03421899e-01 -5.46111512e-01 -5.13084654e-01 -5.45091655e-01\n", "  -8.00154015e-01 -7.67405279e-01 -8.22881856e-01 -7.37783760e-01\n", "  -7.27873331e-01 -8.22579125e-01 -5.05176682e-01  1.08669710e+00\n", "  -1.17453227e+00  9.13083859e-01 -2.34340760e-01 -3.90089210e-01\n", "  -1.41207579e+00 -2.98894398e-01]\n", " [ 3.06741217e-02  4.06909993e-01 -8.48045207e-01 -6.42313756e-01\n", "  -6.92905874e-01 -5.86578628e-01 -6.10598984e-01 -5.87806642e-01\n", "  -6.70973018e-01 -6.25201552e-01 -8.02155756e-01 -6.50776557e-01\n", "  -4.36659954e-01 -8.02181464e-01 -4.55234148e-01  9.55179736e-01\n", "  -2.89311159e-01 -7.33511145e-01 -1.31945747e+00  6.58539234e-01\n", "  -3.56170194e-01 -1.10152526e+00]\n", " [-9.03620847e-01 -7.54386522e-01 -1.71826435e-01 -5.68229550e-01\n", "  -4.03421899e-01 -5.52856032e-01 -4.76968236e-01 -5.52960205e-01\n", "  -6.67297136e-01 -6.55673779e-01 -6.98525254e-01 -6.43458194e-01\n", "  -6.23990197e-01 -6.98219192e-01 -5.20987635e-01  9.94612154e-01\n", "  -1.36115228e+00  8.50127207e-01 -3.01977155e-01 -1.38194222e+00\n", "  -9.99217246e-01 -3.58085364e-01]\n", " [ 4.06669708e-01  1.81723918e-01 -9.54547032e-01 -3.43919039e-01\n", "  -4.03421899e-01 -2.79702999e-01 -4.01123758e-01 -2.79809105e-01\n", "  -5.40741769e-01 -4.98233938e-01 -6.83720897e-01 -5.23111782e-01\n", "  -3.16882133e-01 -6.84072427e-01 -3.50329729e-01  9.41047887e-01\n", "  -7.98083959e-01  7.95363295e-02 -4.55607344e-01  1.82631394e-02\n", "   5.87850650e-01 -6.62905308e-01]\n", " [ 1.16405519e+00  4.40236279e+00  1.42518645e+00  2.06793343e+00\n", "   1.04399798e+00  1.97633872e+00  2.23176313e+00  1.97509257e+00\n", "   2.19201395e+00  2.78768790e+00  1.90802860e+00  1.99440504e+00\n", "   2.28757591e+00  1.90840449e+00  1.35474353e+00 -1.54775306e+00\n", "  -7.09733545e-01  1.07708430e+00  7.34268828e-01  4.49050459e-01\n", "   1.45740193e+00  7.21788856e-01]\n", " [-1.04677392e+00 -7.30427291e-01 -3.02790472e-01  3.33128284e-01\n", "   7.54514002e-01  6.42674871e-02  6.10135953e-01  6.41589480e-02\n", "   3.77703610e-01  2.53414334e-01  4.44371133e-01  3.98188788e-01\n", "   2.11048551e-01  4.44707815e-01 -3.81951635e-01 -8.14597646e-02\n", "   4.10261252e-01  1.90830145e+00  1.15271500e+00  1.62086254e-01\n", "  -1.22108493e+00  1.45341988e+00]\n", " [ 1.30519439e+00  3.23448264e-01  1.93412205e+00 -2.70032708e-02\n", "  -4.03421899e-01  1.18223642e-01 -2.19013664e-02  1.19238799e-01\n", "  -6.48392600e-01 -6.55673779e-01 -6.21542596e-01 -6.13371591e-01\n", "  -6.37614214e-01 -6.21892460e-01 -4.16836119e-01  1.43054287e-01\n", "  -1.58813583e+00 -7.43051135e-02 -7.20878596e-01 -4.83083656e-01\n", "   3.50825345e-01 -7.87118364e-01]\n", " [ 1.63405567e+00  4.53694851e-01  2.08554921e+00 -7.32861118e-01\n", "  -9.82389849e-01 -6.10184446e-01 -7.26171522e-01 -6.09164135e-01\n", "  -7.10357468e-01 -7.06460824e-01 -7.05433954e-01 -5.72714019e-01\n", "  -6.98354626e-01 -7.05128077e-01 -5.39810199e-01  9.29195369e-01\n", "  -1.32548835e+00 -4.03234504e-01 -1.17697406e+00 -1.54196638e+00\n", "  -2.55937839e-01 -1.16118993e+00]\n", " [ 1.99343677e+00  6.42404796e-01  2.54701035e+00 -6.67008491e-01\n", "  -9.82389849e-01 -5.52856032e-01 -5.88929133e-01 -5.52960205e-01\n", "  -6.53118734e-01 -6.09965438e-01 -5.39625152e-01 -6.41018740e-01\n", "  -7.36388342e-01 -5.39972822e-01 -4.39423195e-01  2.18044260e-01\n", "  -1.91225087e+00 -1.13561080e+00 -1.49421632e+00 -1.50887031e+00\n", "   1.18867953e-01 -1.25358559e+00]\n", " [-8.96716127e-01 -6.50082978e-01 -1.15792537e-01  7.71459834e-01\n", "   1.33348195e+00  5.70106437e-01  1.27467805e+00  5.68870241e-01\n", "   1.31085252e+00  1.18281727e+00  1.32473692e+00  1.78298567e+00\n", "   7.61685931e-01  1.32443920e+00 -2.85580112e-01 -2.58791676e-01\n", "  -6.37699260e-01  1.86954069e+00  1.44850511e+00  1.30894995e+00\n", "   8.82542871e-02  1.83615354e+00]\n", " [ 9.92658052e-02  2.94776938e+00 -1.03763904e+00 -3.00703252e-01\n", "  -4.03421899e-01 -4.01104347e-01 -4.98638087e-01 -4.01209594e-01\n", "  -8.07505779e-01 -6.91224711e-01 -9.63029772e-01 -8.66261686e-01\n", "  -6.34775877e-01 -9.63059791e-01 -2.35135642e-01  1.04316189e+00\n", "  -4.82396795e-01 -1.14196473e+00 -3.33626823e-01  5.80656984e-01\n", "   7.26474885e-01 -4.06843472e-01]\n", " [-6.64928501e-01 -6.93065532e-01 -8.28878224e-02 -3.58324301e-01\n", "  -1.13937924e-01 -6.81001899e-01 -6.35880476e-01 -6.81105166e-01\n", "  -5.05558327e-01 -5.03312643e-01 -4.33033779e-01 -6.93873582e-01\n", "  -4.63340322e-01 -4.33049598e-01 -5.06180552e-01  7.40922671e-01\n", "   7.39606260e-02  7.97675468e-01 -9.21533544e-01  8.04097319e-01\n", "   1.59606592e-01 -1.12995903e+00]\n", " [ 3.90534719e-01  8.95931017e-01 -8.60019061e-01 -1.05203266e-01\n", "  -4.03421899e-01 -3.40403673e-01 -2.74716294e-01 -3.40509349e-01\n", "   1.45597917e-01 -1.57570064e-02  1.29531800e-01  1.23343604e-01\n", "   2.23537234e-01  1.29531053e-01 -1.48489110e-03 -7.38362808e-01\n", "   1.73821556e+00 -5.73315778e-01  8.22420092e-01  2.06996807e+00\n", "   1.60278158e+00  8.01349275e-01]\n", " [-1.81162845e-03 -3.85622184e-01  2.87740401e-01 -9.03666370e-01\n", "  -9.82389849e-01 -8.66476180e-01 -8.85083762e-01 -8.65454057e-01\n", "  -1.03225971e+00 -9.65474756e-01 -1.09824290e+00 -1.00043167e+00\n", "  -8.85117202e-01 -1.09860554e+00 -5.64154047e-01  1.51110844e+00\n", "  -2.31851560e+00 -4.56323457e-01 -1.31084141e+00 -9.56886291e-01\n", "  -8.34379856e-01 -1.27432724e+00]\n", " [ 6.69840246e-01  5.01784081e-02 -8.68344632e-01  4.52486171e-01\n", "   1.75546052e-01  5.83595475e-01  3.75379234e-01  5.82359184e-01\n", "  -2.58749106e-01 -2.69692234e-01 -2.07020590e-01 -1.88906545e-01\n", "  -2.83389756e-01 -2.07359349e-01 -1.70135057e-01 -6.82747144e-01\n", "   8.82918797e-01  2.97772705e-01  2.24949904e-01 -9.84574036e-01\n", "  -5.39125101e-01  1.05799023e-01]\n", " [-4.76051943e-03 -2.36919077e-01 -8.93110867e-01 -2.92471674e-01\n", "  -4.03421899e-01 -3.40403673e-01 -2.81939578e-01 -3.40509349e-01\n", "   5.99306783e-01  4.36247698e-01  4.33514604e-01  6.33189551e-01\n", "   1.18913948e+00  4.33193006e-01 -1.61100227e-01 -5.21142614e-01\n", "   1.26615650e+00 -3.20360392e-01  4.62889195e-01  1.53997699e-01\n", "   5.00583796e-01  6.04399454e-01]\n", " [-7.10456501e-01 -6.83170098e-01  2.75429242e-02 -3.93308509e-01\n", "  -4.03421899e-01 -6.47279302e-01 -6.43103760e-01 -6.48506886e-01\n", "  -4.26264301e-01 -4.42368188e-01 -3.36311978e-01 -6.41018740e-01\n", "  -4.23035937e-01 -3.35996211e-01 -5.04925715e-01  7.64855641e-01\n", "   5.21648509e-01  1.08118067e+00 -8.85885965e-01  3.55924394e-01\n", "  -1.44556526e+00 -1.15738899e+00]\n", " [-7.84778143e-01 -6.29146695e-01 -4.22529020e-01  1.27338824e-01\n", "   4.65030027e-01  1.85668835e-01 -4.71828592e-02  1.85559437e-01\n", "  -2.42470200e-01 -2.18905188e-01 -2.38603219e-01 -3.36086953e-01\n", "  -2.12431331e-01 -2.38942824e-01  4.23654070e-01 -5.91223259e-02\n", "   6.56386570e-01 -8.38796952e-01  7.59429494e-01  2.59077116e-01\n", "  -9.75312868e-01 -4.24043591e-01]\n", " [ 5.32297258e-01  4.06886376e-01  2.17323845e-01 -2.10155890e-01\n", "  -4.03421899e-01 -1.54929391e-01 -1.19415696e-01 -1.53912301e-01\n", "   1.84595591e+00  1.50277565e+00  2.06199391e+00  1.90821099e+00\n", "   1.27883093e+00  2.06171594e+00  5.85528113e-01 -1.79095762e+00\n", "   1.00966068e+00  4.42219807e-01  1.60501330e-01  8.94973905e-01\n", "   2.07145770e+00  1.66817150e-01]\n", " [ 6.14530559e-01  3.89752397e-01  6.67933677e-01  1.78786189e-01\n", "  -1.13937924e-01  1.95785614e-01  2.67029979e-01  1.94552066e-01\n", "   6.91728960e-01  8.01914425e-01  7.05914780e-01  7.77117354e-01\n", "   5.81735364e-01  7.06258468e-01  1.18759917e+00 -2.25958797e+00\n", "   8.92681003e-01  5.27588153e-01  1.20712824e+00  1.80412264e+00\n", "   3.52184931e+00  1.42009113e+00]\n", " [ 3.52414909e-01  4.63566668e-01 -9.52161616e-01  1.97307240e-01\n", "  -1.13937924e-01 -1.65046170e-01  1.51457441e-01 -1.64029008e-01\n", "   1.73954721e-01  3.24516198e-01 -1.38920546e-01  6.56098529e-02\n", "   5.67543679e-01 -1.39257481e-01  1.07435008e-01 -3.19877732e-01\n", "   1.28658355e+00 -2.02324862e+00  2.54192270e+00  2.50762348e+00\n", "   1.72124268e+00  2.53332794e+00]\n", " [ 3.99836912e-01  3.03857593e+00 -8.71969530e-01 -1.38129579e-01\n", "  -4.03421899e-01 -2.99936557e-01 -3.03609429e-01 -3.01166598e-01\n", "  -6.67297136e-01 -5.54099688e-01 -8.13999242e-01 -6.84115765e-01\n", "  -4.41768961e-01 -8.14354262e-01 -1.72895700e-01  8.91358483e-01\n", "  -1.29780040e-01 -7.37789576e-01  1.00582909e+00  1.76323716e+00\n", "   1.63558193e+00  8.63980265e-01]\n", " [-9.09326831e-01 -7.22586517e-01 -1.52064897e-01 -4.96203239e-01\n", "  -4.03421899e-01 -5.12388916e-01 -4.08347041e-01 -5.13617454e-01\n", "   1.58917940e-02 -8.68588700e-02 -6.68726740e-02 -2.55412949e-04\n", "   1.92883194e-01 -6.65496889e-02 -1.66621512e-01 -6.78872283e-01\n", "   1.22489033e+00 -3.73194459e-01 -1.63244669e-01  9.95315746e-02\n", "   1.97024501e-01 -2.39026705e-01]\n", " [-1.19102983e+00  1.54434719e-01 -8.96852697e-01  2.23861996e-02\n", "   4.65030027e-01 -5.71338609e-02  1.08117739e-01 -5.72415412e-02\n", "   4.05010162e-01  4.92113448e-01  1.48283986e-01  3.51839156e-01\n", "   7.66227270e-01  1.48612735e-01  9.94040476e-02  6.53291181e-02\n", "   2.23503881e-01  3.89622419e-01  1.08180122e-01  1.44796370e-01\n", "   1.28419218e+00  1.50417824e-01]\n", " [-3.56134065e-01  4.64829621e+00 -1.17306988e+00  4.91605956e+00\n", "   5.09677363e+00  5.12940150e+00  4.63350494e+00  5.13038121e+00\n", "   3.37302233e+00  3.28032224e+00  3.91747337e+00  2.59613710e+00\n", "   2.03950525e+00  3.91790310e+00  5.88244836e+00 -2.57345177e+00\n", "   9.45769741e-01 -1.36781125e+00  2.23003680e+00  5.49080512e-01\n", "   8.62448505e-01  2.69107277e+00]\n", " [-9.47590490e-01 -7.81073493e-01 -5.89250916e-01  5.94283024e-02\n", "   4.65030027e-01  1.11479122e-01 -1.82897246e-02  1.10246171e-01\n", "   3.47771428e-01  4.31168993e-01  2.78562331e-01  1.03827970e-01\n", "   3.75104430e-01  2.78565576e-01  5.42298960e-02 -6.43998526e-01\n", "   1.81690188e+00  5.51401716e-02  1.46896637e-01  2.15355846e-01\n", "   9.67595904e-02  1.61764263e-01]\n", " [-1.08537322e+00 -6.88046964e-01 -3.14390144e-01  2.85796708e-01\n", "   7.54514002e-01  5.41507081e-02  5.09009982e-01  5.29181619e-02\n", "  -1.45321889e-01 -1.47803325e-01 -9.74683459e-02 -1.65325153e-01\n", "  -2.06186989e-01 -9.71461805e-02 -3.65136812e-01 -1.71265385e-01\n", "   3.53630648e-01  1.86584485e+00  9.58146311e-01 -2.29658235e-01\n", "  -1.04756318e+00  1.24655603e+00]\n", " [ 1.17728924e+00  2.19664288e-01  1.86723684e+00 -9.13955843e-01\n", "  -1.01133825e+00 -8.05775506e-01 -8.63413911e-01 -8.04753812e-01\n", "  -1.07006878e+00 -1.01118310e+00 -1.09922986e+00 -9.77663431e-01\n", "  -9.67428975e-01 -1.09959252e+00 -6.08826264e-01  2.56826191e+00\n", "  -1.56879783e+00  4.61554672e-01 -2.09734422e+00 -7.32668210e-01\n", "  -2.57665122e+00 -1.82308435e+00]\n", " [-5.93196129e-01 -6.26513423e-01  5.34784808e-02 -2.71892728e-01\n", "  -1.13937924e-01 -5.66345070e-01 -5.85317491e-01 -5.66449148e-01\n", "  -2.61374736e-01 -2.84928347e-01 -1.83333619e-01 -4.82454211e-01\n", "  -2.74307077e-01 -1.83342748e-01 -5.03670877e-01  8.31867957e-01\n", "  -7.80806327e-01  1.25925442e+00 -1.05761734e+00  5.39807391e-01\n", "  -4.05066871e-01 -6.61912777e-01]\n", " [ 8.06855790e-01  1.11003446e-01  1.32595780e+00 -6.29966388e-01\n", "  -6.92905874e-01 -5.56228291e-01 -5.88929133e-01 -5.56332441e-01\n", "  -6.99854948e-01 -6.75988597e-01 -7.42938326e-01 -6.58094920e-01\n", "  -6.04121838e-01 -7.42633454e-01 -5.58632762e-01  1.03267697e+00\n", "  -1.01225400e+00  7.60589667e-01 -2.92536261e-01  5.04222537e-01\n", "   2.58278024e-02 -5.13033059e-01]\n", " [-4.24845622e-01 -1.92814725e-01 -1.18258816e+00  7.17954574e-01\n", "   7.54514002e-01  6.78018746e-01  4.80116847e-01  6.76781787e-01\n", "   1.04093775e+00  1.01014131e+00  1.15201941e+00  5.31545622e-01\n", "   7.41249904e-01  1.15204606e+00  2.18362647e-01 -7.31524817e-01\n", "   4.25154748e-01  3.03762508e-01  4.43512506e-01  1.20059687e-03\n", "   7.30484722e-01  4.15096637e-01]\n", " [ 1.44352854e+00  7.95335308e-01 -8.96735764e-01 -1.13434844e-01\n", "  -4.03421899e-01 -1.27951314e-01 -1.01357487e-01 -1.28058493e-01\n", "  -5.83802102e-01 -4.77919120e-01 -5.48507766e-01 -5.29616994e-01\n", "  -6.00148166e-01 -5.48526679e-01  4.78615954e-01 -1.30009504e-01\n", "  -3.64612595e-01 -9.50181798e-01 -3.20997344e-02 -4.07833776e-01\n", "   4.94997697e-01 -6.49754266e-01]\n", " [ 2.12831857e+00  7.33423898e-01  2.60977954e+00 -8.17234797e-01\n", "  -1.01133825e+00 -7.28213534e-01 -7.44229731e-01 -7.29440546e-01\n", "  -7.88601243e-01 -7.62326574e-01 -7.25173098e-01 -7.58925697e-01\n", "  -8.03940763e-01 -7.24867749e-01 -5.05678617e-01  7.41378538e-01\n", "  -6.73843949e-01 -1.41314441e+00 -1.20416041e+00 -1.46776944e+00\n", "  -1.95989277e-02 -1.16703233e+00]\n", " [ 2.29616376e-01  9.34915976e-02 -9.24659168e-01  2.46696711e-01\n", "   1.75546052e-01  1.58690758e-01  1.62292367e-01  1.58581551e-01\n", "  -7.01955452e-01 -6.09965438e-01 -8.32751428e-01 -7.02818248e-01\n", "  -5.29757408e-01 -8.32777956e-01 -1.78918920e-01  4.67630947e-01\n", "  -1.00102991e+00  2.90053324e-01  1.21447510e-01 -4.62059903e-02\n", "   8.65864191e-01  1.10344366e-01]\n", " [-7.61019193e-01 -5.96968821e-01 -5.03352539e-01  2.15828292e-01\n", "   4.65030027e-01  2.76719846e-01 -2.19013664e-02  2.77733883e-01\n", "  -3.69025566e-01 -3.10321870e-01 -3.71842435e-01 -4.25533611e-01\n", "  -3.74216540e-01 -3.71856615e-01  1.75698170e-01  2.67505731e-01\n", "   1.09604884e+00 -1.68982233e+00  2.34202791e-01  6.91635992e-02\n", "   8.86127703e-01  1.62057511e-01]\n", " [-1.06012784e+00 -7.86033019e-01 -2.77837146e-01  5.18338798e-01\n", "   1.04399798e+00  2.19391432e-01  7.00426998e-01  2.18157717e-01\n", "   1.11989853e-01  1.16289312e-01  2.04540544e-01  1.46111844e-01\n", "   1.35002953e-02  2.04870801e-01 -3.28997491e-01 -2.30755911e-01\n", "   5.75139509e-01  1.73496129e+00  1.24940030e+00 -3.61910197e-02\n", "  -9.72393663e-01  1.56387284e+00]\n", " [ 7.69215474e-01  5.66848333e-02  1.43281042e+00 -6.67008491e-01\n", "  -6.92905874e-01 -5.66345070e-01 -6.14210625e-01 -5.65325070e-01\n", "  -7.88076117e-01 -7.57247870e-01 -7.97220970e-01 -7.08510308e-01\n", "  -6.79621602e-01 -7.97575541e-01 -5.67165657e-01  1.14937869e+00\n", "  -1.70211326e+00  8.93475903e-01 -8.83036371e-01 -2.83059447e-01\n", "   2.84584882e-03 -8.52941245e-01]]\n"]}]}, {"cell_type": "markdown", "source": ["**SVM**"], "metadata": {"id": "rZl19r2sMBwE"}}, {"cell_type": "code", "source": ["svm_model = svm.SVC(kernel = \"linear\")"], "metadata": {"id": "EDCBlJRzMEcP"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["svm_model.fit(x_train,y_train)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 74}, "id": "xT3U18mtL7rN", "executionInfo": {"status": "ok", "timestamp": 1708863311306, "user_tz": -330, "elapsed": 579, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}, "outputId": "2b487f34-dd86-4ee9-d221-3b247fc059c1"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["SVC(kernel='linear')"], "text/html": ["<style>#sk-container-id-2 {color: black;background-color: white;}#sk-container-id-2 pre{padding: 0;}#sk-container-id-2 div.sk-toggleable {background-color: white;}#sk-container-id-2 label.sk-toggleable__label {cursor: pointer;display: block;width: 100%;margin-bottom: 0;padding: 0.3em;box-sizing: border-box;text-align: center;}#sk-container-id-2 label.sk-toggleable__label-arrow:before {content: \"▸\";float: left;margin-right: 0.25em;color: #696969;}#sk-container-id-2 label.sk-toggleable__label-arrow:hover:before {color: black;}#sk-container-id-2 div.sk-estimator:hover label.sk-toggleable__label-arrow:before {color: black;}#sk-container-id-2 div.sk-toggleable__content {max-height: 0;max-width: 0;overflow: hidden;text-align: left;background-color: #f0f8ff;}#sk-container-id-2 div.sk-toggleable__content pre {margin: 0.2em;color: black;border-radius: 0.25em;background-color: #f0f8ff;}#sk-container-id-2 input.sk-toggleable__control:checked~div.sk-toggleable__content {max-height: 200px;max-width: 100%;overflow: auto;}#sk-container-id-2 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {content: \"▾\";}#sk-container-id-2 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {background-color: #d4ebff;}#sk-container-id-2 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {background-color: #d4ebff;}#sk-container-id-2 input.sk-hidden--visually {border: 0;clip: rect(1px 1px 1px 1px);clip: rect(1px, 1px, 1px, 1px);height: 1px;margin: -1px;overflow: hidden;padding: 0;position: absolute;width: 1px;}#sk-container-id-2 div.sk-estimator {font-family: monospace;background-color: #f0f8ff;border: 1px dotted black;border-radius: 0.25em;box-sizing: border-box;margin-bottom: 0.5em;}#sk-container-id-2 div.sk-estimator:hover {background-color: #d4ebff;}#sk-container-id-2 div.sk-parallel-item::after {content: \"\";width: 100%;border-bottom: 1px solid gray;flex-grow: 1;}#sk-container-id-2 div.sk-label:hover label.sk-toggleable__label {background-color: #d4ebff;}#sk-container-id-2 div.sk-serial::before {content: \"\";position: absolute;border-left: 1px solid gray;box-sizing: border-box;top: 0;bottom: 0;left: 50%;z-index: 0;}#sk-container-id-2 div.sk-serial {display: flex;flex-direction: column;align-items: center;background-color: white;padding-right: 0.2em;padding-left: 0.2em;position: relative;}#sk-container-id-2 div.sk-item {position: relative;z-index: 1;}#sk-container-id-2 div.sk-parallel {display: flex;align-items: stretch;justify-content: center;background-color: white;position: relative;}#sk-container-id-2 div.sk-item::before, #sk-container-id-2 div.sk-parallel-item::before {content: \"\";position: absolute;border-left: 1px solid gray;box-sizing: border-box;top: 0;bottom: 0;left: 50%;z-index: -1;}#sk-container-id-2 div.sk-parallel-item {display: flex;flex-direction: column;z-index: 1;position: relative;background-color: white;}#sk-container-id-2 div.sk-parallel-item:first-child::after {align-self: flex-end;width: 50%;}#sk-container-id-2 div.sk-parallel-item:last-child::after {align-self: flex-start;width: 50%;}#sk-container-id-2 div.sk-parallel-item:only-child::after {width: 0;}#sk-container-id-2 div.sk-dashed-wrapped {border: 1px dashed gray;margin: 0 0.4em 0.5em 0.4em;box-sizing: border-box;padding-bottom: 0.4em;background-color: white;}#sk-container-id-2 div.sk-label label {font-family: monospace;font-weight: bold;display: inline-block;line-height: 1.2em;}#sk-container-id-2 div.sk-label-container {text-align: center;}#sk-container-id-2 div.sk-container {/* jupyter's `normalize.less` sets `[hidden] { display: none; }` but bootstrap.min.css set `[hidden] { display: none !important; }` so we also need the `!important` here to be able to override the default hidden behavior on the sphinx rendered scikit-learn.org. See: https://github.com/scikit-learn/scikit-learn/issues/21755 */display: inline-block !important;position: relative;}#sk-container-id-2 div.sk-text-repr-fallback {display: none;}</style><div id=\"sk-container-id-2\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>SVC(kernel=&#x27;linear&#x27;)</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item\"><div class=\"sk-estimator sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-2\" type=\"checkbox\" checked><label for=\"sk-estimator-id-2\" class=\"sk-toggleable__label sk-toggleable__label-arrow\">SVC</label><div class=\"sk-toggleable__content\"><pre>SVC(kernel=&#x27;linear&#x27;)</pre></div></div></div></div></div>"]}, "metadata": {}, "execution_count": 24}]}, {"cell_type": "code", "source": ["x_train_prediction = svm_model.predict(x_train)\n", "training_data_accuracy = accuracy_score(y_train, x_train_prediction)"], "metadata": {"id": "WuSndYyNMMfc"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["print(\"Accuracy on Training Data :\", training_data_accuracy)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "x9YxMD-SMQTo", "executionInfo": {"status": "ok", "timestamp": 1708863342964, "user_tz": -330, "elapsed": 509, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}, "outputId": "152d8efb-5291-4921-e615-b8d32dd8399a"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Accuracy on Training Data : 0.9102564102564102\n"]}]}, {"cell_type": "code", "source": ["x_test_prediction = svm_model.predict(x_test)\n", "test_data_accuracy = accuracy_score(y_test, x_test_prediction)"], "metadata": {"id": "x4lMSobcMUS0"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["print(\"Accuracy on Test Data :\", test_data_accuracy)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "x-2Ql4pNMYCo", "executionInfo": {"status": "ok", "timestamp": 1708863377229, "user_tz": -330, "elapsed": 421, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}, "outputId": "4c68fc5e-d1ba-4268-be56-e4eda7fd2690"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Accuracy on Test Data : 0.8461538461538461\n"]}]}, {"cell_type": "code", "source": ["print(\"Classification Report\")\n", "print(classification_report(y_test,x_test_prediction))\n", "print(\"Confusion Matrix\")\n", "print(confusion_matrix(y_test, x_test_prediction))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "e_pX1t_1MclE", "executionInfo": {"status": "ok", "timestamp": 1708863392866, "user_tz": -330, "elapsed": 457, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}, "outputId": "bad510ca-d9bc-49cb-b9e1-8b03289a576f"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Classification Report\n", "              precision    recall  f1-score   support\n", "\n", "           0       1.00      0.40      0.57        10\n", "           1       0.83      1.00      0.91        29\n", "\n", "    accuracy                           0.85        39\n", "   macro avg       0.91      0.70      0.74        39\n", "weighted avg       0.87      0.85      0.82        39\n", "\n", "Confusion Matrix\n", "[[ 4  6]\n", " [ 0 29]]\n"]}]}, {"cell_type": "code", "source": ["plt.figure(figsize = (5,5))\n", "\n", "sns.heatmap(confusion_matrix(x_train_prediction, y_train), annot = True, fmt = \"g\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 462}, "id": "vHgd2Sv9MgbQ", "executionInfo": {"status": "ok", "timestamp": 1708863409702, "user_tz": -330, "elapsed": 20, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}, "outputId": "dc08dfae-0aa3-46f5-bf70-0ae618d4515b"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["<Axes: >"]}, "metadata": {}, "execution_count": 30}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 500x500 with 2 Axes>"], "image/png": "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\n"}, "metadata": {}}]}, {"cell_type": "markdown", "source": ["**LogisticRegression**"], "metadata": {"id": "AjQdD7uiMqax"}}, {"cell_type": "code", "source": ["logistic_model = LogisticRegression()"], "metadata": {"id": "_RaY3LffMsSu"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["logistic_model.fit(x_train, y_train)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 74}, "id": "7Br9EfkIMwSt", "executionInfo": {"status": "ok", "timestamp": 1708863474849, "user_tz": -330, "elapsed": 410, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}, "outputId": "fcddd672-06f5-490d-9f83-a4910b1b16d3"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["LogisticRegression()"], "text/html": ["<style>#sk-container-id-3 {color: black;background-color: white;}#sk-container-id-3 pre{padding: 0;}#sk-container-id-3 div.sk-toggleable {background-color: white;}#sk-container-id-3 label.sk-toggleable__label {cursor: pointer;display: block;width: 100%;margin-bottom: 0;padding: 0.3em;box-sizing: border-box;text-align: center;}#sk-container-id-3 label.sk-toggleable__label-arrow:before {content: \"▸\";float: left;margin-right: 0.25em;color: #696969;}#sk-container-id-3 label.sk-toggleable__label-arrow:hover:before {color: black;}#sk-container-id-3 div.sk-estimator:hover label.sk-toggleable__label-arrow:before {color: black;}#sk-container-id-3 div.sk-toggleable__content {max-height: 0;max-width: 0;overflow: hidden;text-align: left;background-color: #f0f8ff;}#sk-container-id-3 div.sk-toggleable__content pre {margin: 0.2em;color: black;border-radius: 0.25em;background-color: #f0f8ff;}#sk-container-id-3 input.sk-toggleable__control:checked~div.sk-toggleable__content {max-height: 200px;max-width: 100%;overflow: auto;}#sk-container-id-3 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {content: \"▾\";}#sk-container-id-3 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {background-color: #d4ebff;}#sk-container-id-3 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {background-color: #d4ebff;}#sk-container-id-3 input.sk-hidden--visually {border: 0;clip: rect(1px 1px 1px 1px);clip: rect(1px, 1px, 1px, 1px);height: 1px;margin: -1px;overflow: hidden;padding: 0;position: absolute;width: 1px;}#sk-container-id-3 div.sk-estimator {font-family: monospace;background-color: #f0f8ff;border: 1px dotted black;border-radius: 0.25em;box-sizing: border-box;margin-bottom: 0.5em;}#sk-container-id-3 div.sk-estimator:hover {background-color: #d4ebff;}#sk-container-id-3 div.sk-parallel-item::after {content: \"\";width: 100%;border-bottom: 1px solid gray;flex-grow: 1;}#sk-container-id-3 div.sk-label:hover label.sk-toggleable__label {background-color: #d4ebff;}#sk-container-id-3 div.sk-serial::before {content: \"\";position: absolute;border-left: 1px solid gray;box-sizing: border-box;top: 0;bottom: 0;left: 50%;z-index: 0;}#sk-container-id-3 div.sk-serial {display: flex;flex-direction: column;align-items: center;background-color: white;padding-right: 0.2em;padding-left: 0.2em;position: relative;}#sk-container-id-3 div.sk-item {position: relative;z-index: 1;}#sk-container-id-3 div.sk-parallel {display: flex;align-items: stretch;justify-content: center;background-color: white;position: relative;}#sk-container-id-3 div.sk-item::before, #sk-container-id-3 div.sk-parallel-item::before {content: \"\";position: absolute;border-left: 1px solid gray;box-sizing: border-box;top: 0;bottom: 0;left: 50%;z-index: -1;}#sk-container-id-3 div.sk-parallel-item {display: flex;flex-direction: column;z-index: 1;position: relative;background-color: white;}#sk-container-id-3 div.sk-parallel-item:first-child::after {align-self: flex-end;width: 50%;}#sk-container-id-3 div.sk-parallel-item:last-child::after {align-self: flex-start;width: 50%;}#sk-container-id-3 div.sk-parallel-item:only-child::after {width: 0;}#sk-container-id-3 div.sk-dashed-wrapped {border: 1px dashed gray;margin: 0 0.4em 0.5em 0.4em;box-sizing: border-box;padding-bottom: 0.4em;background-color: white;}#sk-container-id-3 div.sk-label label {font-family: monospace;font-weight: bold;display: inline-block;line-height: 1.2em;}#sk-container-id-3 div.sk-label-container {text-align: center;}#sk-container-id-3 div.sk-container {/* jupyter's `normalize.less` sets `[hidden] { display: none; }` but bootstrap.min.css set `[hidden] { display: none !important; }` so we also need the `!important` here to be able to override the default hidden behavior on the sphinx rendered scikit-learn.org. See: https://github.com/scikit-learn/scikit-learn/issues/21755 */display: inline-block !important;position: relative;}#sk-container-id-3 div.sk-text-repr-fallback {display: none;}</style><div id=\"sk-container-id-3\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>LogisticRegression()</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item\"><div class=\"sk-estimator sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-3\" type=\"checkbox\" checked><label for=\"sk-estimator-id-3\" class=\"sk-toggleable__label sk-toggleable__label-arrow\">LogisticRegression</label><div class=\"sk-toggleable__content\"><pre>LogisticRegression()</pre></div></div></div></div></div>"]}, "metadata": {}, "execution_count": 32}]}, {"cell_type": "code", "source": ["x_train_prediction = logistic_model.predict(x_train)\n", "training_data_accuracy = accuracy_score(y_train, x_train_prediction)"], "metadata": {"id": "a1yhdCz1M0cr"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["print(\"Accuracy on Training Data :\", training_data_accuracy)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "6mwF8EMKM5Km", "executionInfo": {"status": "ok", "timestamp": 1708863508763, "user_tz": -330, "elapsed": 528, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}, "outputId": "f48afd04-5f64-4a6c-a2ca-4e07644034dc"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Accuracy on Training Data : 0.8717948717948718\n"]}]}, {"cell_type": "code", "source": ["x_test_prediction = logistic_model.predict(x_test)\n", "test_data_accuracy = accuracy_score(y_test, x_test_prediction)"], "metadata": {"id": "sjhjXwg7M8pj"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["print(\"Accuracy on Test Data :\", test_data_accuracy)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ExbSPoM-NAJB", "executionInfo": {"status": "ok", "timestamp": 1708863535656, "user_tz": -330, "elapsed": 12, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}, "outputId": "e9ef6dcf-3b27-4b47-a902-63488c776e22"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Accuracy on Test Data : 0.8205128205128205\n"]}]}, {"cell_type": "code", "source": ["print(\"Mean Absolute Error: \", (mean_absolute_error(y_test, x_test_prediction)))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ozZ-K6zQNDLq", "executionInfo": {"status": "ok", "timestamp": 1708863553725, "user_tz": -330, "elapsed": 441, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}, "outputId": "3d6103fe-835e-4ece-b5e9-d527b39ce941"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Mean Absolute Error:  0.1794871794871795\n"]}]}, {"cell_type": "code", "source": ["print(\"Mean Squared Error: \", (mean_squared_error(y_test, x_test_prediction)))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "2vpxzgYcNHtR", "executionInfo": {"status": "ok", "timestamp": 1708863567975, "user_tz": -330, "elapsed": 427, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}, "outputId": "db04b520-af52-4c8d-a889-0cec19badfeb"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Mean Squared Error:  0.1794871794871795\n"]}]}, {"cell_type": "code", "source": ["print(\"Median Absolute Error: \", (median_absolute_error(y_test, x_test_prediction)))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "gBvRNdzYNLMq", "executionInfo": {"status": "ok", "timestamp": 1708863583965, "user_tz": -330, "elapsed": 478, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}, "outputId": "2af2dd4f-a37a-4914-d031-5cbe49da35f0"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Median Absolute Error:  0.0\n"]}]}, {"cell_type": "code", "source": ["print(\"Classification Report\")\n", "print(classification_report(y_test,x_test_prediction))\n", "print(\"Confusion Matrix\")\n", "print(confusion_matrix(y_test, x_test_prediction))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "4QH7X99YNPEz", "executionInfo": {"status": "ok", "timestamp": 1708863598012, "user_tz": -330, "elapsed": 444, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}, "outputId": "29177441-0da5-4708-df7c-75d902287d9e"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Classification Report\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.80      0.40      0.53        10\n", "           1       0.82      0.97      0.89        29\n", "\n", "    accuracy                           0.82        39\n", "   macro avg       0.81      0.68      0.71        39\n", "weighted avg       0.82      0.82      0.80        39\n", "\n", "Confusion Matrix\n", "[[ 4  6]\n", " [ 1 28]]\n"]}]}, {"cell_type": "code", "source": ["plt.figure(figsize = (5,5))\n", "\n", "sns.heatmap(confusion_matrix(x_train_prediction, y_train), annot = True, fmt = \"g\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 462}, "id": "DnRTKT4PNSkA", "executionInfo": {"status": "ok", "timestamp": 1708863614380, "user_tz": -330, "elapsed": 954, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}, "outputId": "d4743613-c3b7-49ae-b6c9-fcf3e421c22e"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["<Axes: >"]}, "metadata": {}, "execution_count": 41}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 500x500 with 2 Axes>"], "image/png": "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\n"}, "metadata": {}}]}, {"cell_type": "markdown", "source": ["**XGB Regressor**"], "metadata": {"id": "hz9i35lXNl7N"}}, {"cell_type": "code", "source": ["regressor_model = XGBRegressor()"], "metadata": {"id": "W2rwNbpGNuSI"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["regressor_model.fit(x_train, y_train)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 248}, "id": "TAMZM0uONu9g", "executionInfo": {"status": "ok", "timestamp": 1708863728517, "user_tz": -330, "elapsed": 453, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}, "outputId": "ab0ed8d4-356c-478b-f349-3c33a13346dd"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["XGBRegressor(base_score=None, booster=None, callbacks=None,\n", "             colsample_bylevel=None, colsample_bynode=None,\n", "             colsample_bytree=None, device=None, early_stopping_rounds=None,\n", "             enable_categorical=False, eval_metric=None, feature_types=None,\n", "             gamma=None, grow_policy=None, importance_type=None,\n", "             interaction_constraints=None, learning_rate=None, max_bin=None,\n", "             max_cat_threshold=None, max_cat_to_onehot=None,\n", "             max_delta_step=None, max_depth=None, max_leaves=None,\n", "             min_child_weight=None, missing=nan, monotone_constraints=None,\n", "             multi_strategy=None, n_estimators=None, n_jobs=None,\n", "             num_parallel_tree=None, random_state=None, ...)"], "text/html": ["<style>#sk-container-id-4 {color: black;background-color: white;}#sk-container-id-4 pre{padding: 0;}#sk-container-id-4 div.sk-toggleable {background-color: white;}#sk-container-id-4 label.sk-toggleable__label {cursor: pointer;display: block;width: 100%;margin-bottom: 0;padding: 0.3em;box-sizing: border-box;text-align: center;}#sk-container-id-4 label.sk-toggleable__label-arrow:before {content: \"▸\";float: left;margin-right: 0.25em;color: #696969;}#sk-container-id-4 label.sk-toggleable__label-arrow:hover:before {color: black;}#sk-container-id-4 div.sk-estimator:hover label.sk-toggleable__label-arrow:before {color: black;}#sk-container-id-4 div.sk-toggleable__content {max-height: 0;max-width: 0;overflow: hidden;text-align: left;background-color: #f0f8ff;}#sk-container-id-4 div.sk-toggleable__content pre {margin: 0.2em;color: black;border-radius: 0.25em;background-color: #f0f8ff;}#sk-container-id-4 input.sk-toggleable__control:checked~div.sk-toggleable__content {max-height: 200px;max-width: 100%;overflow: auto;}#sk-container-id-4 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {content: \"▾\";}#sk-container-id-4 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {background-color: #d4ebff;}#sk-container-id-4 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {background-color: #d4ebff;}#sk-container-id-4 input.sk-hidden--visually {border: 0;clip: rect(1px 1px 1px 1px);clip: rect(1px, 1px, 1px, 1px);height: 1px;margin: -1px;overflow: hidden;padding: 0;position: absolute;width: 1px;}#sk-container-id-4 div.sk-estimator {font-family: monospace;background-color: #f0f8ff;border: 1px dotted black;border-radius: 0.25em;box-sizing: border-box;margin-bottom: 0.5em;}#sk-container-id-4 div.sk-estimator:hover {background-color: #d4ebff;}#sk-container-id-4 div.sk-parallel-item::after {content: \"\";width: 100%;border-bottom: 1px solid gray;flex-grow: 1;}#sk-container-id-4 div.sk-label:hover label.sk-toggleable__label {background-color: #d4ebff;}#sk-container-id-4 div.sk-serial::before {content: \"\";position: absolute;border-left: 1px solid gray;box-sizing: border-box;top: 0;bottom: 0;left: 50%;z-index: 0;}#sk-container-id-4 div.sk-serial {display: flex;flex-direction: column;align-items: center;background-color: white;padding-right: 0.2em;padding-left: 0.2em;position: relative;}#sk-container-id-4 div.sk-item {position: relative;z-index: 1;}#sk-container-id-4 div.sk-parallel {display: flex;align-items: stretch;justify-content: center;background-color: white;position: relative;}#sk-container-id-4 div.sk-item::before, #sk-container-id-4 div.sk-parallel-item::before {content: \"\";position: absolute;border-left: 1px solid gray;box-sizing: border-box;top: 0;bottom: 0;left: 50%;z-index: -1;}#sk-container-id-4 div.sk-parallel-item {display: flex;flex-direction: column;z-index: 1;position: relative;background-color: white;}#sk-container-id-4 div.sk-parallel-item:first-child::after {align-self: flex-end;width: 50%;}#sk-container-id-4 div.sk-parallel-item:last-child::after {align-self: flex-start;width: 50%;}#sk-container-id-4 div.sk-parallel-item:only-child::after {width: 0;}#sk-container-id-4 div.sk-dashed-wrapped {border: 1px dashed gray;margin: 0 0.4em 0.5em 0.4em;box-sizing: border-box;padding-bottom: 0.4em;background-color: white;}#sk-container-id-4 div.sk-label label {font-family: monospace;font-weight: bold;display: inline-block;line-height: 1.2em;}#sk-container-id-4 div.sk-label-container {text-align: center;}#sk-container-id-4 div.sk-container {/* jupyter's `normalize.less` sets `[hidden] { display: none; }` but bootstrap.min.css set `[hidden] { display: none !important; }` so we also need the `!important` here to be able to override the default hidden behavior on the sphinx rendered scikit-learn.org. See: https://github.com/scikit-learn/scikit-learn/issues/21755 */display: inline-block !important;position: relative;}#sk-container-id-4 div.sk-text-repr-fallback {display: none;}</style><div id=\"sk-container-id-4\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>XGBRegressor(base_score=None, booster=None, callbacks=None,\n", "             colsample_bylevel=None, colsample_bynode=None,\n", "             colsample_bytree=None, device=None, early_stopping_rounds=None,\n", "             enable_categorical=False, eval_metric=None, feature_types=None,\n", "             gamma=None, grow_policy=None, importance_type=None,\n", "             interaction_constraints=None, learning_rate=None, max_bin=None,\n", "             max_cat_threshold=None, max_cat_to_onehot=None,\n", "             max_delta_step=None, max_depth=None, max_leaves=None,\n", "             min_child_weight=None, missing=nan, monotone_constraints=None,\n", "             multi_strategy=None, n_estimators=None, n_jobs=None,\n", "             num_parallel_tree=None, random_state=None, ...)</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item\"><div class=\"sk-estimator sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-4\" type=\"checkbox\" checked><label for=\"sk-estimator-id-4\" class=\"sk-toggleable__label sk-toggleable__label-arrow\">XGBRegressor</label><div class=\"sk-toggleable__content\"><pre>XGBRegressor(base_score=None, booster=None, callbacks=None,\n", "             colsample_bylevel=None, colsample_bynode=None,\n", "             colsample_bytree=None, device=None, early_stopping_rounds=None,\n", "             enable_categorical=False, eval_metric=None, feature_types=None,\n", "             gamma=None, grow_policy=None, importance_type=None,\n", "             interaction_constraints=None, learning_rate=None, max_bin=None,\n", "             max_cat_threshold=None, max_cat_to_onehot=None,\n", "             max_delta_step=None, max_depth=None, max_leaves=None,\n", "             min_child_weight=None, missing=nan, monotone_constraints=None,\n", "             multi_strategy=None, n_estimators=None, n_jobs=None,\n", "             num_parallel_tree=None, random_state=None, ...)</pre></div></div></div></div></div>"]}, "metadata": {}, "execution_count": 43}]}, {"cell_type": "code", "source": ["training_data_prediction = regressor_model.predict(x_train)\n", "\n", "score_1 = metrics.r2_score(y_train, training_data_prediction)\n", "print(\"R squared error : \", score_1)\n", "\n", "score_2 = metrics.mean_absolute_error(y_train, training_data_prediction)\n", "print(\"Mean Absolute Error : \", score_2)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "_X-Hug1LNyYE", "executionInfo": {"status": "ok", "timestamp": 1708863762179, "user_tz": -330, "elapsed": 439, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}, "outputId": "9769e398-02e9-4cd1-c82a-32643f0a6882"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["R squared error :  0.9999981802671856\n", "Mean Absolute Error :  0.0003437339594781336\n"]}]}, {"cell_type": "code", "source": ["test_data_prediction = regressor_model.predict(x_test)\n", "\n", "score_1 = metrics.r2_score(y_test, test_data_prediction)\n", "print(\"R squared error : \", score_1)\n", "\n", "score_2 = metrics.mean_absolute_error(y_test, test_data_prediction)\n", "print(\"Mean Absolute Error : \", score_2)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "m8sb0ZGNN6na", "executionInfo": {"status": "ok", "timestamp": 1708863777387, "user_tz": -330, "elapsed": 482, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}, "outputId": "fcc301f7-95c2-49ab-c69e-3d3584a8ab30"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["R squared error :  0.40966613945680086\n", "Mean Absolute Error :  0.15626078149020592\n"]}]}, {"cell_type": "code", "source": ["input_data = (197.07600,206.89600,192.05500,0.00289,0.00001,0.00166,0.00168,0.00498,0.01098,0.09700,0.00563,0.00680,0.00802,0.01689,0.00339,26.77500,0.422229,0.741367,-7.348300,0.177551,1.743867,0.085569)\n", "\n", "# changing input data to a numpy array\n", "input_data_as_numpy_array = np.asarray(input_data)\n", "\n", "# reshape the numpy array\n", "input_data_reshaped = input_data_as_numpy_array.reshape(1,-1)\n", "\n", "# standardize the data\n", "std_data = scaler.transform(input_data_reshaped)\n", "\n", "prediction = svm_model.predict(std_data)\n", "print(prediction)\n", "\n", "\n", "if (prediction[0] == 0):\n", "  print(\"The Person does not have Parkinsons Disease\")\n", "\n", "else:\n", "  print(\"The Person has Parkinsons\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "XkC2TNilN-Oq", "executionInfo": {"status": "ok", "timestamp": 1708863793846, "user_tz": -330, "elapsed": 11, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}, "outputId": "376f3c90-bc91-4035-b9f4-42b2d1ae9f2f"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["[0]\n", "The Person does not have Parkinsons Disease\n"]}, {"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.10/dist-packages/sklearn/base.py:439: UserWarning: X does not have valid feature names, but StandardScaler was fitted with feature names\n", "  warnings.warn(\n"]}]}, {"cell_type": "code", "source": ["import pickle"], "metadata": {"id": "yXzSj1VOOCRS"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["filename = \"parkinsons_disease_model.sav\"\n", "pickle.dump(svm_model, open(filename, \"wb\"))"], "metadata": {"id": "d46y5DCVOFnT"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["#loading the saved model\n", "loaded_model = pickle.load(open(\"heart_disease_model.sav\", \"rb\"))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 158}, "id": "YCF5ntKfOJKW", "executionInfo": {"status": "error", "timestamp": 1708863835256, "user_tz": -330, "elapsed": 446, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}, "outputId": "c06849e0-8da5-4473-ccaa-cf081daa4f75"}, "execution_count": null, "outputs": [{"output_type": "error", "ename": "FileNotFoundError", "evalue": "[Errno 2] No such file or directory: 'heart_disease_model.sav'", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mFileNotFoundError\u001b[0m                         <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-49-e0eb755c6ef8>\u001b[0m in \u001b[0;36m<cell line: 2>\u001b[0;34m()\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[0;31m#loading the saved model\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m----> 2\u001b[0;31m \u001b[0mloaded_model\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mpickle\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mload\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mopen\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m\"heart_disease_model.sav\"\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m\"rb\"\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[0;31mFileNotFoundError\u001b[0m: [Errno 2] No such file or directory: 'heart_disease_model.sav'"]}]}, {"cell_type": "code", "source": ["input_data = (197.07600,206.89600,192.05500,0.00289,0.00001,0.00166,0.00168,0.00498,0.01098,0.09700,0.00563,0.00680,0.00802,0.01689,0.00339,26.77500,0.422229,0.741367,-7.348300,0.177551,1.743867,0.085569)\n", "\n", "# changing input data to a numpy array\n", "input_data_as_numpy_array = np.asarray(input_data)\n", "\n", "# reshape the numpy array\n", "input_data_reshaped = input_data_as_numpy_array.reshape(1,-1)\n", "\n", "# standardize the data\n", "std_data = scaler.transform(input_data_reshaped)\n", "\n", "prediction = svm_model.predict(std_data)\n", "print(prediction)\n", "\n", "\n", "if (prediction[0] == 0):\n", "  print(\"The Person does not have Parkinsons Disease\")\n", "\n", "else:\n", "  print(\"The Person has Parkinsons\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "i0IPbvUoOMfm", "executionInfo": {"status": "ok", "timestamp": 1708863884258, "user_tz": -330, "elapsed": 543, "user": {"displayName": "<PERSON><PERSON><PERSON>", "userId": "15606167662174339940"}}, "outputId": "a2fb238d-11e3-4e1c-ec11-18a2b4cec525"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["[0]\n", "The Person does not have Parkinsons Disease\n"]}, {"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.10/dist-packages/sklearn/base.py:439: UserWarning: X does not have valid feature names, but StandardScaler was fitted with feature names\n", "  warnings.warn(\n"]}]}, {"cell_type": "code", "source": [], "metadata": {"id": "etP8O0rpOYQS"}, "execution_count": null, "outputs": []}]}